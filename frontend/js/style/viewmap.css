:root {
	/*
	Brand colors as raw rgb values
	Used to derive colors with alpha
	*/
	/* TODO: replace these colours with neutrals to be overridden */
	--color-primary-rgb: 93, 127, 164;
	--color-secondary-rgb: 116, 189, 191;
	--color-background-rgb: 236, 238, 241;
	--color-text-rgb: 22, 36, 50;
	--color-neutral-light-rgb: 228, 235, 242;
	--color-neutral-mid-rgb: 183, 194, 206;
	--color-neutral-dark-rgb: 109, 120, 132;


	/* Brand colors */
	--color-primary: rgb(var(--color-primary-rgb));
	--color-primary-transparent: rgba(var(--color-primary-rgb), 0.7);
	--color-secondary: rgb(var(--color-secondary-rgb));
	--color-background: rgb(var(--color-background-rgb));
	--color-text: rgb(var(--color-text-rgb));
	--color-neutral-light: rgb(var(--color-neutral-light-rgb));
	--color-neutral-mid: rgb(var(--color-neutral-mid-rgb));
	--color-neutral-dark: rgb(var(--color-neutral-dark-rgb));

	/* Additional colors */
	--color-black-translucent: rgba(0, 0, 0, .5);
	--color-black-semi-translucent: rgba(0, 0, 0, .333);
	--color-backdrop: rgba(0, 0, 0, .55);
	--color-white-translucent: rgba(255, 255, 255, .5);
	--color-white-semi-opaque: rgba(255, 255, 255, .9);
	--color-highlight: var(--color-secondary);
	--color-highlight-bg: rgba(var(--color-secondary-rgb), .15);
	--color-label-border: rgb(102, 102, 102);
	--color-invalid: rgb(255, 165, 0);
	--color-placeholder: rgb(117, 117, 117);

	--color-rag-negative-rgb: 226, 5, 22;
	--color-rag-neutral-rgb: 238, 170, 13;
	--color-rag-positive-rgb: 60, 164, 34;

	--color-rag-negative: rgb(var(--color-rag-negative-rgb));
	--color-rag-neutral: rgb(var(--color-rag-neutral-rgb));
	--color-rag-positive: rgb(var(--color-rag-positive-rgb));

	--color-rag-negative-overlay: rgba(var(--color-rag-negative-rgb), .66);
	--color-rag-neutral-overlay: rgba(var(--color-rag-neutral-rgb), .66);
	--color-rag-positive-overlay: rgba(var(--color-rag-positive-rgb), .66);

	--color-score-1: rgb(230, 7, 7);
	--color-score-2: rgb(179, 28, 50);
	--color-score-3: rgb(128, 48, 93);
	--color-score-4: rgb(78, 69, 136);
	--color-score-5: rgb(27, 90, 139);

	/* Preset glosses */
	--gloss-finish-fg-default: rgba(242, 242, 242, 0.53);
	--gloss-finish-dt-default: 10, 1;

	--gloss-finish-fg-causal: rgb(68, 68, 68);
	--gloss-finish-fg-from: rgb(124, 124, 121);
	--gloss-finish-fg-parental: rgba(255, 255, 255, 0.53);

	--gloss-finish-fg-negative: var(--color-rag-negative);
	--gloss-finish-fg-neutral: var(--color-rag-neutral);
	--gloss-finish-fg-positive: var(--color-rag-positive);
	--gloss-finish-fg-unknown: var(--color-neutral-dark);

	--color-skeleton-light-grey: rgb(219, 219, 219);
	--color-skeleton-dark-grey: rgb(203, 203, 203);

	/* Icons */
	--logo: local; /* a no-op value */
	--icon-info: url(//opatlas-live.s3.amazonaws.com/EOMNewIcons/20072020/Info.png);
	--icon-less: url(//opatlas-live.s3.amazonaws.com/EOMNewIcons/20072020/Less.png);
	--icon-more: url(//opatlas-live.s3.amazonaws.com/EOMNewIcons/20072020/More.png);
	--gone-wrong: url(//opatlas-live.s3.amazonaws.com/icons/gone_wrong.png);

	--icon-send: url(//opatlas-live.s3.amazonaws.com/icons/Send-Mask.svg);
	--icon-send-fill: url(//opatlas-live.s3.amazonaws.com/icons/Send-Inverse-Mask.svg);

	--icon-nav-up: url(//opatlas-live.s3.amazonaws.com/icons/Navigate%20up.svg);

	--icon-unknown-marker: url(//opatlas-live.s3.amazonaws.com/icons/unknown%20marker.png);

	/* Sizes */
	--gap: 3rem;
	--spacing: 1.5rem;
	--half-spacing: 0.75rem;
	--quarter-spacing: 0.375rem;
	--eighth-spacing: 0.187rem;
	--minus-spacing: -1.5rem;
	--line-height: 1.5;
	--highlight-border: 0.333em;
	--bar-max: 28rem;
	--panel-main-max: 28rem;
	--bar-collapsed: 2rem;
	--comments-max: 19.75rem;

	--outline-width: 0.125rem;
	--outline-offset: -0.125rem;

	--anim-duration: .2s;
	--inline-spacing: 0.4em;
	--text-sub-1-size: 0.875rem;
	--text-sub-2-size: 0.75rem;
	--text-heading-1-size: 1.375rem;
	--text-heading-2-size: 1.125rem;
	--panel-spacing: 0.5em;
	--panel-minus-spacing: -0.5em;
	--panel-inner-spacing: 1.25rem;
	--panel-inner-half-spacing: 0.5rem;
}

/*
 * Styles for general layout, some specific to this board
 */

body {
	background: var(--color-background);
	color: var(--color-text);
	font-family: var(--font-body);
	line-height: 1.5;
}

body.no-transitions * {
	transition: none !important;
}

h1,
h2,
h3,
h4,
h5 {
	font-family: var(--font-head);
}

div[tabindex="-1"] {
	outline: none;
}

/*
 * Styles for headings, text blocks, and general typography flow.
 */

h1,
h2,
h3,
h4,
h5,
p,
dl,
ol,
ul {
	margin: var(--spacing);
}

ul, ol {
	padding: 0 0 0 var(--spacing);
}

ul ol,
ul ul,
ol ul,
ol ol {
	margin: 0;
}

* + h1,
* + h2,
* + h3 {
	margin-top: var(--gap);
}

h1 {
	font-size: 2em;
	font-weight: 800;
}
h2 { font-size: 1.6em; }
h3 { font-size: 1.4em; }
h4 { font-size: 1.2em; }
h5 { font-size: 1em; }

h1 + p {
	font-size: 1.2em;
}

nav section {
	position: relative;
}

div:focus-visible,
section:focus-visible,
span:focus-visible {
	outline: 0;
}

/*
 * Styles for <a/> elements and linking
 */

a {
	color: inherit;
	text-decoration: none;
	cursor: pointer;
}

a:visited {
	color: inherit;
}

a:focus {
	background-color: transparent;
	color: black;
}

a[aria-disabled="true"] {
	pointer-events: none;
	cursor: default;
	color: rgba(var(--color-primary-rgb), 0.5) !important;
}

.ol-control a:link,
.vm-c a:link,
.vm-d a:link,
.vm-h a:link {
	color: var(--color-primary);
	transition: background-color 0.2s ease, color 0.2s ease;
}

.ol-control a:visited,
.vm-c a:visited,
.vm-d a:visited,
.vm-h a:visited {
	color: var(--color-primary);
}

.vm-c a:focus,
.vm-d a:focus {
	background-color: white;
}

.ol-control a:focus,
.vm-c a:focus:visited,
.vm-d a:focus:visited,
.vm-h a:focus:visited {
	color: inherit;
}

@media (hover: hover) and (pointer: fine) {
	a:hover {
		background-color: transparent;
		color: black;
	}

	.vm-c a:hover,
	.vm-d a:hover {
		background-color: white;
	}

	.ol-control a:hover,
	.vm-c a:hover:visited,
	.vm-d a:hover:visited,
	.vm-h a:hover:visited {
		color: inherit;
	}
}

/*
 * Styles for input elements
 */

input:focus::placeholder {
	color: transparent;
}

input::placeholder {
	color: var(--color-placeholder);
}

input[type="search"]::-webkit-search-cancel-button {
	display: none;
}

@media (hover: hover) and (pointer: fine) {
	[type="search"]::-webkit-search-cancel-button:hover {
		cursor: pointer;
	}
}

/*
 * Styles for user created markdown content
 */

.vm-c > *:first-child {
	margin-top: 0;
}

.vm-c * {
	overflow-wrap: break-word;
}

/*
 * Styles for specific components (not inline for now as CSS-in-JS is still...
 */


.vm-errorreport {
	background: var(--gone-wrong) top / auto 18em no-repeat;
	padding-top: 18em;
	max-width: 36em;
	margin: var(--spacing) auto;
}

.vm-errorreport details {
	font-family: var(--font-code);
	margin: var(--spacing) auto;
}

.vm-errorreport pre {
	font-family: inherit;
	white-space: pre-wrap;
}

.vm-errorreport button {
	display: block;
	font-size: var(--text-heading-1-size);
	margin: var(--spacing) auto;
}

.vm-layer {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}

.vm-map-placeholder {
	overflow: hidden;
	background: white;
}

.vm-map-placeholder img {
	width: 100%;
	height: 100%;
	object-fit: contain;
	filter: grayscale(1);
}

.vm-map-container {
	width: 100%;
	height: 100%;
	background: white;
}

.vm-map-container:focus-visible .ol-layer::after {
	content: "";
	outline: 2px solid var(--color-primary);
	outline-offset: -2px;
	height: 100%;
	width: 100%;
	pointer-events: none;
	position: absolute;
	z-index: 8;
}

.vm-map-container.faded canvas {
	/* Openlayers 6.7.0 overrides canvas styling with an inline setting */
	filter: grayscale(1) !important;
}

.vm-layer.vm-float {
	overflow: hidden;
	pointer-events: none;
}

.vm-float-container {
	position: absolute;
	top: var(--panel-spacing);
	right: var(--panel-spacing);
	bottom: var(--panel-spacing);
	left: var(--panel-spacing);
}

.vm-full-items-container {
	height: calc(100% - 4.5rem);
	position: relative;
	overflow: hidden;
	top: 0;
	margin-top: 4.5rem;
}

@media (max-width: 55.999em) {
	.vm-full-items-container {
		margin: 4.5rem var(--panel-spacing);
		height: calc(100% - 9rem);
	}

	.has-secondary-control .vm-full-items-container {
		margin: 7.5rem var(--panel-spacing) 4.5rem var(--panel-spacing);
		height: calc(100% - 12rem);
	}
}

.vm-full-items-container > div {
	height: 100%;
}

.vm-full-items {
	display: flex;
	flex-direction: column;
	background: white;
}

.vm-full-items-content {
	width: 100%;
	height: calc(100% - 3.5em);
	overflow-y: auto;
}

.vm-full-items-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: var(--half-spacing) var(--spacing);
	font-size: var(--text-sub-1-size);
}

.vm-full-items-header div {
	display: flex;
	align-items: center;
	gap: 0.5em;
	font-weight: 700;
	line-height: 1.1;
	font-size: var(--text-heading-1-size);
}

.vm-full-items-header > div > svg {
	width: 1.1em;
}

.vm-float-container > .hide-bottom {
	transform: translateY(150%);
}

.vm-float-block {
	pointer-events: auto;
	color: var(--color-text);
	background: white;
	box-sizing: border-box;
	box-shadow: 0 0.25em 1em rgba(49, 49, 104, 0.4);
	border-radius: 0.375em;
	overflow: hidden;
}

.vm-float-block-skeleton {
	background: var(--color-skeleton-light-grey);
	box-sizing: border-box;
	border-radius: 0.375em;
	overflow: hidden;
}

.vm-float-block-skeleton .ol-zoom-in,
.vm-float-block-skeleton .ol-zoom-out {
	background: var(--color-skeleton-light-grey);
}

.vm-float-block-skeleton .vm-float-search-bar > div > input,
.vm-float-block-skeleton .vm-float-search-bar > button {
	background: var(--color-skeleton-dark-grey) !important;
}

.vm-shimmer-wrapper {
	background-color: #fff;
	border-radius: 0.375em;
}

.vm-shimmer-normalized {
	--shimmer-duration: 5s;
	-webkit-mask: linear-gradient(
		-80deg,
		#000 min(calc(50% - 20px), max(40%, calc(50% - 70px))),
		#0005,
		#000 min(calc(50% + 20px), max(60%, calc(50% + 70px)))
	) right/300%;
	mask: linear-gradient(
		-80deg,
		#000 min(calc(50% - 20px), max(40%, calc(50% - 70px))),
		#0005,
		#000 min(calc(50% + 20px), max(60%, calc(50% + 70px)))
	) right/250%;
	background-repeat: no-repeat;
	animation: shimmer var(--shimmer-duration) infinite;
}

.vm-shimmer {
	--shimmer-duration: 5s;
	-webkit-mask: linear-gradient(
		-70deg,
		#000 40%,
		#0005,
		#000 60%
	) right/250%;
	mask: linear-gradient(
		-70deg,
		#000 40%,
		#0005,
		#000 60%
	) right/250%;
	background-repeat: no-repeat;
	animation: shimmer var(--shimmer-duration) infinite;
}

@keyframes shimmer {
	100% {
		-webkit-mask-position: left;
		mask-position: left;
	}
}

.vm-float-block .vm-t,
.vm-intro-tooltip .vm-t {
	font-weight: 700;
	font-size: var(--text-heading-1-size);
	line-height: 1.1;
}

.vm-float-container.annotations {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.vm-float-container.context {
	display: flex;
	align-items: flex-start;
	justify-content: flex-end;
}

.vm-float-container-side {
	display: flex;
	flex-direction: column;
	flex-shrink: 0;
	max-height: min(75vh, calc(100vh - 4.5em - 3 * var(--panel-spacing)));
	max-height: min(75dvh, calc(100dvh - 4.5em - 3 * var(--panel-spacing)));
	min-height: 0;
}

.vm-float-container-side > * + * {
	margin-top: var(--panel-spacing);
}

.vm-button-group {
	display: flex;
	flex-direction: row;
	align-items: flex-start;
}

.vm-button-group > * + * {
	margin-left: var(--panel-spacing);
}

.vm-float-block.comments {
	width: min(var(--panel-main-max), calc(100vw - 2 * var(--panel-spacing)));
	display: flex;
	flex-direction: column;
	padding: var(--panel-inner-half-spacing) var(--panel-inner-spacing);
}

.vm-float-block.comments .disabled,
.vm-float-block.comments .disabled button {
	pointer-events: none;
}

.vm-float-block.comments .disabled {
	filter: opacity(0.4);
}

.vm-float-block.comments > * {
	font-size: var(--text-sub-1-size);
}

.vm-float-block .comments-body button {
	pointer-events: auto;
	cursor: pointer;
	display: flex;
}

.vm-float-block.comments textarea,
.vm-float-block.comments select {
	outline: 0;
}

.vm-float-block.comments textarea:focus-visible,
.vm-float-block.comments select:focus-visible {
	border: 0.0625rem solid var(--color-primary);
}

.vm-panel-header {
	display: flex;
	align-items: center;
	gap: var(--panel-inner-half-spacing);
	min-height: 2rem;
	flex-shrink: 0;
}

.vm-panel-header .vm-t {
	display: flex;
	align-items: center;
	flex-grow: 1;
	min-width: 0;
	font-weight: 700;
	line-height: 1.1;
	padding-top: 0.25em;
}

.vm-panel-header .vm-t .vm-i-container {
	margin-right: var(--inline-spacing);
	display: flex;
	align-items: center;
}

.vm-panel-header .vm-t .vm-l {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-width: 0;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.vm-panel-header .vm-t .vm-l .title {
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
}

.vm-panel-header-controls {
	display: flex;
	align-items: center;
	flex-direction: row-reverse;
	flex-shrink: 0;
	margin-left: auto;
	gap: var(--panel-inner-spacing);
}

.vm-panel-header .vm-close-button {
	flex-shrink: 0;
}

.vm-panel-header .vm-i-container svg {
	height: 1.5rem;
	width: 1.5rem;
}

.vm-collapse-button {
	color: var(--color-primary);
	background: none;
	border: none;
	font-weight: bold;
	text-decoration-line: underline;
	cursor: pointer;
}

.vm-panel-header + *:not(:empty) {
	margin-top: var(--panel-inner-half-spacing);
}

*:not(:empty) + .vm-panel-header{
	margin-bottom: var(--panel-inner-half-spacing);
}

.vm-info-block {
	--font-info-block: 1rem;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0.75rem;
	border-radius: 0.25rem;
	font-size: var(--font-info-block);
}

.vm-info-block > * + * {
	margin-left: calc(0.5 * var(--font-info-block));
}

.vm-info-block svg,
.vm-info-block img {
	height: var(--font-info-block);
	width: var(--font-info-block);
}

.comments-body {
	line-height: 1.375;
	overflow-y: auto;
}

.comments-body > * + .vm-info-block {
	margin-top: 1.25rem;
}

.comments-body .no-comments {
	color: rgba(var(--color-text-rgb), 0.7);
	font-size: 1rem;
	font-weight: bold;
	border: 0.0625rem solid rgba(var(--color-text-rgb), 0.3);
}

.comments-body .selection-info {
	background: var(--color-background);
	color: var(--color-text);
}

.comments-body > .comments-item:not(:last-of-type):after {
	display: block;
	content: "";
	width: 100%;
	border-top: 0.0625rem solid rgba(var(--color-text-rgb), 0.3);
	margin: var(--panel-inner-half-spacing) 0;
}

.comments-item .vm-accordion-header {
	margin: 0.0625rem 0;
}

.comments-item .vm-expand-button[aria-pressed="false"] {
	color: rgba(var(--color-text-rgb), 0.5);
}

.comments-item .vm-t {
	font-weight: bold;
	font-size: 1rem;
	line-height: 1.5;
}

.comments-item[aria-current="true"] .vm-t {
	font-size: var(--text-heading-2-size); /* tentative */
}

.comments-item .vm-label-tag {
	--color-category-rgb: var(--color-text-rgb);
	--color-category: rgb(var(--color-category-rgb));
	--color-category-background: rgba(var(--color-category-rgb), 0.08);
	color: var(--color-category);
	background: var(--color-category-background);
	font-weight: normal;
	font-style: italic;
	display: inline-block;
	margin: 0.25rem 0;
}

.comments-item .vm-label-tag img {
	height: 1rem;
	width: 1rem;
	margin-right: 0.5rem;
}

.comments-item .buttons,
.comments-item .delete-buttons {
	display: flex;
	align-items: center;
	justify-content: flex-end;
	flex: 1;
}

.comments-item .buttons button {
	display: flex;
	align-items: center;
	color: var(--color-primary);
	background: none;
	border: none;
	padding: 0;
}

.comments-item .buttons > * + * {
	margin-left: 0.75rem;
}

.comments-item .buttons svg {
	height: 1.25rem;
	width: 1.25rem;
}

.comments-item .vm-accordion-content {
	display: flex;
	flex-direction: column;
}

.comments-item .comment-add {
	margin: var(--spacing) 0;
}

.comments-item .comment-add:not(:first-child):before {
	display: block;
	content: "";
	width: 100%;
	border-top: 0.0625rem dashed rgba(var(--color-text-rgb), 0.3);
	margin-bottom: var(--spacing);
}

.comments-item .comment-editor:first-child {
	margin-top: 0.5rem;
}

.comments-item button.vm-comment-add-button {
	border: 0.0625rem solid var(--color-primary);
	padding: 0.675rem;
	margin-top: var(--spacing);
}

.comments-item .vm-comment-add-button,
.comments-item .delete-buttons button,
.comment-editor button {
	border-radius: 0.25rem;
	font-weight: bold;
}

.comments-item button.vm-comment-add-button svg {
	height: 1rem;
	width: 1rem;
}

/* animation has not finished yet for .vm-accordion-header */
.comment-content {
	margin-top: 0.75rem;
}

.comment-content .date {
	font-style: italic;
	color: rgba(var(--color-text-rgb), 0.6);
}

.comment-content .text {
	font-size: 1rem;
	color: rgba(var(--color-text-rgb), 1);
	max-height: 10rem;
	overflow-y: auto;
}

.comment-content .text p {
	margin: 0;
}

.comment-content .misc {
	display: flex;
	align-items: center;
	margin: 0.125rem 0;
}

.comment-editor {
	display: flex;
	flex-direction: column;
	margin-top: var(--half-spacing);
}

.comment-editor > *:not(:first-child) {
	margin-top: var(--half-spacing);
}

.comment-editor .header {
	font-weight: bold;
}

.comment-editor button,
.comment-editor textarea,
.comment-editor select {
	box-sizing: border-box;
	outline: 0;
}

.comment-editor textarea,
.comment-editor select {
	padding: 0.5rem;
	border-radius: 0.375rem;
	background: var(--color-background);
}

.comment-editor textarea {
	resize: none;
	min-height: 5rem;
	padding: 0.625rem;
}

.comment-editor textarea::placeholder {
	font-style: italic;
}

.comment-editor select img {
	height: 1rem;
	width: 1rem;
}

.comment-editor button {
	font-size: var(--text-sub-1-size);
}

.comment-editor .add-mode-buttons {
	display: flex;
	justify-content: flex-end;
}

.comment-editor .add-mode-buttons button {
	flex: 0 1 calc(0.5 * (100% - var(--half-spacing)));
	border: 0.0625rem solid var(--color-primary);
	padding: 0.75rem;
	line-height: 1;
	align-items: center;
	justify-content: center;
}

.vm-confirm-button {
	--color-confirm-button-rgb: var(--color-primary-rgb);
	background: rgb(var(--color-confirm-button-rgb));
	color: white;
	display: flex;
	align-items: center;
	justify-content: center;
}

button.vm-cancel-button {
	border: none;
	background: none;
	color: var(--color-primary);
}

.comments-item .edit-mode-buttons .vm-cancel-button,
.comments-item .delete-buttons .vm-cancel-button {
	margin-right: 0.5rem;
	margin-left: var(--half-spacing);
}

.add-mode-buttons .vm-cancel-button {
	background: none;
	color: var(--color-primary);
}

.comment-editor .add-mode-buttons > * + * {
	margin-left: var(--half-spacing);
}

.comment-editor .edit-mode-buttons {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.comments-item .vm-confirm-button,
.comment-editor .edit-mode-buttons .vm-confirm-button {
	line-height: 1;
	padding: 0.5rem 1rem;
	border: none;
}

.edit-mode-buttons .vm-confirm-button {
	--color-confirm-button-rgb: 0, 133, 102;
}

.delete-buttons .vm-confirm-button {
	--color-confirm-button-rgb: 199, 0, 76;
}

@media (hover: hover) and (pointer: fine) {
	.vm-confirm-button:hover {
		background: rgba(var(--color-confirm-button-rgb), 0.7);
	}

	.vm-cancel-button:hover {
		color: rgba(var(--color-primary-rgb), 0.7);
	}

	.add-mode-buttons .vm-cancel-button:hover {
		background: rgba(var(--color-primary-rgb), 0.7);
		color: white;
	}
}

.vm-float-container.base {
	display: flex;
	flex-direction: column-reverse;
	justify-content: space-between;
	z-index: 0;
}

.vm-float-container.base > * + * {
	margin-bottom: var(--panel-spacing);
}

.vm-float-selected-item {
	padding: var(--panel-inner-half-spacing) var(--panel-inner-spacing);
	min-height: 3.75em;
	display: flex;
	flex-direction: column;
	overflow-wrap: break-word;
}

.vm-float-selected-item.non-expandable .vm-expand-button {
	display: none;
}

.vm-float-selected-item > header,
.vm-float-selected-item > header .vm-t,
.vm-float-selected-item > header .vm-t .vm-i-container {
	align-items: flex-start;
}

.vm-float-selected-item > header .vm-t .vm-i {
	height: 1.15em;
}

.vm-float-selected-item > header .vm-t .vm-l .title {
	white-space: normal;
}

.vm-float-selected-item > header .vm-explain {
	white-space: pre-wrap;
	font-style: italic;
	font-weight: normal;
	font-size: var(--text-sub-1-size);
	color: var(--color-neutral-dark);
	margin-top: 0.25em;
}

.vm-float-selected-item > header .vm-explain .vm-object-link {
	display: inline;
	vertical-align: bottom;
}

.vm-float-selected-item .vm-float-block-content.collapse {
	overflow: hidden;
}

.vm-float-selected-item:not(.non-expandable) .vm-float-block-content.collapse {
	-webkit-mask-image: linear-gradient(180deg, #000 calc(100% - 1.5rem), transparent);
	mask-image: linear-gradient(180deg, #000 calc(100% - 1.5rem), transparent);
}

.vm-float-selected-item .vm-object-link {
	font-style: normal;
	line-height: 1.1;
}

.vm-object-link {
	display: inline-block;
	vertical-align: top;
	max-width: 100%;
}

.vm-object-link a {
	font-weight: bold;
	color: var(--color-primary);
}

.vm-d .vm-object-link a {
	display: inline-flex;
	align-items: center;
	max-width: 100%;
}

.vm-d .vm-object-link span {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-right: var(--eighth-spacing);
	max-width: 100%;
}

.relation-other .vm-object-link a {
	display: inline-flex;
	align-items: center;
	max-width: 100%;
}

.relation-other .vm-object-link span {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	margin-right: var(--eighth-spacing)
}

.vm-object-link svg {
	flex-shrink: 0;
}

/* object-link markdown */
.vm-object-link > div p {
	margin: 0;
}

.vm-object-link > div ul {
	list-style-type: revert;
	padding: revert;
}

.selected-item-content {
	font-size: var(--text-sub-1-size);
	padding-right: 0.5rem;
}

.selected-item-content:not(:empty) {
	background: var(--color-background);
	padding: var(--panel-inner-half-spacing);
	margin-top: var(--panel-inner-half-spacing);
}

.selected-item-content > * + * {
	margin-top: var(--panel-inner-half-spacing);
}

.selected-item-content .vm-t {
	line-height: 1;
	display: flex;
	align-items: flex-start;
}

.selected-item-content .vm-c {
	font-size: 1em;
}

.selected-item-content .vm-c > p,
.selected-item-content .vm-d > p {
	margin: 0;
}

.selected-item-content button.vm-filter-toggle.vm-toggle-button {
	min-width: 50%;
	justify-content: space-between;
	border: 0.0625rem solid var(--color-primary);
	border-radius: 0.25rem;
	padding: 0.375em 0.625em;
	--color-toggle: var(--color-primary);
	--color-toggle-inverse: #fff;
}

.vm-label-tag {
	padding: 0.375em 0.5em;
	font-size: var(--text-sub-1-size);
	border-radius: 1em;
	font-weight: bold;
	font-style: normal;
	box-sizing: border-box;
}

.vm-float-selected-item .footer {
	display: flex;
	flex-direction: column;
	font-size: unset;
	margin-top: 1rem;
}


.buttons-trail {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
}

.vm-pagination .vm-toggle-button,
.buttons-trail .vm-toggle-button,
.vm-intro-tooltip-arrows-progress .vm-toggle-button {
	height: 2em;
	width: 2em;
	border-radius: 1em;
	--color-toggle: var(--color-primary);
	--color-toggle-inverse: var(--color-background);
}

.buttons-trail .vm-toggle-button svg {
	height: 1em;
	width: 1em;
}

.vm-float-selected-item .buttons-trail .navigation > * + * {
	margin-left: var(--inline-spacing);
}

.vm-float-selected-item .buttons-trail .context > * + * {
	margin-left: var(--half-spacing);
}

.vm-float-selected-item .buttons-trail > div {
	display: flex;
	flex-direction: row;
}

.vm-float-selected-item .buttons-trail .context {
	justify-content: flex-end;
	min-width: 0;
	margin-left: var(--half-spacing);
}

.vm-float-selected-item .buttons-trail .context .vm-expand-button {
	flex-shrink: 0;
}

.vm-float-selected-item .buttons-trail .vm-toggle-button.vm-focus-button svg {
	height: 1.5em;
	width: 1.5em;
}

.vm-relation-group-container .relation-group {
	margin: 0;
	display: flex;
	align-items: flex-start;
	flex-direction: column;
}

.selected-item-content .vm-relation-group-container:not(:first-child):before,
.vm-relation-group-container .relation-group:not(:first-child):before {
	display: block;
	content: "";
	width: 100%;
	border-top: 0.0625rem dotted rgba(var(--color-text-rgb), 0.3);
	margin-top: var(--panel-inner-half-spacing);
	margin-bottom: var(--panel-inner-half-spacing);
}

.relation-group > .relation-label {
	font-style: italic;
	color: rgba(var(--color-text-rgb), 0.8);
}

.relation-group > .relation-other {
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	width: calc(100% - var(--half-spacing));
	margin-left: var(--half-spacing);
	font-style: normal;
}

.relation-other .vm-object-link {
	margin: calc(var(--panel-inner-half-spacing) / 2) 0;
}

.vm-search.vm-float-block-section {
	flex-direction: column-reverse;
}

.vm-search > header {
	display: flex;
	flex-direction: row;
}

.vm-search ul {
	list-style-type: none;
	margin: 0;
	padding: 0;
}

.vm-search-results header {
	font-size: var(--text-heading-2-size);
	font-weight: bold;
	margin-bottom: var(--panel-inner-half-spacing);
}

.vm-search-results .vm-result-group:not(:last-of-type):after {
	display: block;
	content: "";
	width: 100%;
	border-top: 0.0625rem solid rgba(var(--color-text-rgb), 0.3);
	margin: var(--panel-inner-half-spacing) 0;
}

.vm-search-recent .vm-search-recent-title {
	flex: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 var(--panel-inner-half-spacing)
}

.vm-search-recent .vm-float-block-content > footer {
	display: flex;
	justify-content: space-between;
	margin-top: var(--panel-inner-half-spacing);
	padding: 0 var(--panel-inner-half-spacing);
}

.vm-search-recent .vm-float-block-content > footer > *:only-child {
	margin-left: auto;
}

.vm-search-recent .vm-search-group > header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: var(--color-neutral-dark);
	font-weight: bold;
	margin-bottom: var(--panel-inner-half-spacing);
}

.vm-search-recent .vm-search-group {
	position: relative;
}

.vm-search .vm-search-item {
	padding: 0.125em var(--panel-inner-half-spacing);
}

.vm-search-item {
	cursor: pointer;
	height: 2.5em;
	display: flex;
	position: relative;
	line-height: 1.25;
}

.vm-search-item:focus-visible,
.vm-search-item:focus-within {
	background-color: var(--color-background);
	outline: none;
}

@media (hover: hover) and (pointer: fine) {
	.vm-search-item:hover {
		background-color: var(--color-background);
		outline: none;
	}
}

.vm-search-item a {
	display: flex;
	align-items: center;
	flex: 1;
	min-width: 0;
}

.vm-search-item .vm-search-item-content {
	height: 100%;
	display: flex;
	align-items: center;
	flex: 1;
	min-width: 0;
}

.vm-search-item .vm-search-item-content .vm-i-container {
	display: flex;
	min-width: 0;
	margin-right: 0.5em;
}

.vm-search-item .vm-i {
	width: 1em;
	height: 1em;
	font-size: 1.25em;
	line-height: 1;
}

.vm-search-item .vm-focus-icon {
	width: 1em;
	height: 1em;
	color: var(--color-neutral-dark);
}

.vm-search-item .vm-recent-content {
	color: var(--color-black-translucent);
}

.vm-search-item .vm-recent-content .vm-i {
	font-size: 1em;
}

.vm-search-item .vm-search-item-content .vm-label {
	display: flex;
	flex-direction: column;
	flex: 1;
	min-width: 0;
}

@media (hover: hover) and (pointer: fine) {
	.vm-search-item a:hover {
		color: inherit;
	}
}

.vm-search-item .vm-search-inline {
	font-size: var(--text-sub-2-size);
	color: var(--color-neutral-dark);
	display: flex;
}

.vm-search-item .vm-search-inline .vm-explain {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.vm-search-recent .vm-label-button.vm-toggle-button {
	font-weight: normal;
}

.vm-search-item .vm-anchored-button {
	display: none;
	position: absolute;
	top: calc(50% - 0.9em);
	right: 0.5em;
	width: 1.8em;
	height: 1.8em;
	border-radius: 0.9em;
	padding: 0.4em;
}

.vm-search-item .vm-anchored-button.vm-toggle-button {
	--color-toggle-inverse: var(--color-background);
}

.vm-search-recent .vm-anchored-button.focused {
	display: flex;
}

.vm-search-recent li:has(.vm-anchored-button.focused) {
	padding-right: calc(1.8em + var(--panel-inner-spacing));
}

@media (hover: hover) and (pointer: fine) {
	.vm-search-recent li:hover .vm-anchored-button {
		display: flex;
	}

	.vm-search-recent li:hover:has(.vm-anchored-button) {
		padding-right: calc(1.8em + var(--panel-inner-spacing));
	}
}

.vm-search-item .vm-anchored-button .vm-svg {
	width: 100%;
	height: 100%;
}


/*
 * Filter Toggles Components
 */

.vm-filter-toggle-container {
	display: flex;
	flex-direction: row;
	height: max-content;
	gap: var(--inline-spacing);
	padding: var(--panel-inner-half-spacing);
	overflow: hidden;
}

.vm-interaction .vm-filter-toggle-container {
	margin-bottom: -0.375rem;
}

.vm-filter-toggle-container .vm-filter-toggle {
	border-radius: 1em;
	display: flex;
	flex-shrink: 0;
}

.vm-filter-toggle-container button .label {
	font-size: var(--text-sub-1-size);
}

.vm-filter-toggle-container button .vm-i {
	font-size: 1.5em;
	height: 1em;
	width: 1em;
	line-height: 1.075;
	font-weight: normal;
}

.vm-filter-toggle-container .vm-toggle-button {
	padding: 0.25em 0.5em;
	--color-toggle: var(--color-primary);
	--color-toggle-inverse: var(--color-background);
}

.vm-filter-toggle-container .vm-restore-button {
	border-radius: 1em;
	display: flex;
	flex-shrink: 0;
	font-size: unset;
	height: 2em;
	width: 2em;
}

.vm-filter-toggle-container button svg {
	height: 1rem;
	width: 1rem;
}

.vm-comments-toggle {
	height: 3em;
}

.vm-comments-toggle button {
	padding: var(--panel-spacing);
	height: 3em;
	min-width: 3em;
	position: relative;
}

.vm-comments-toggle span {
	position: absolute;
	bottom: 0.4rem;
	right: 0.6rem;
	line-height: 1;
	font-size: var(--text-sub-1-size);
}

.vm-float-map-select-button svg:first-child {
	height: 1.75em;
	width: 1.75em;
}

.vm-comments-toggle button svg {
 	position: absolute;
	top: 0.7rem;
	left: 0.7rem;
	height: 1.25em;
	width: 1.25em;
}

.vm-toggle-icon {
	width: 1em;
	height: 1em;
	display: flex;
	align-items: center;
}

.vm-toggle-icon.vm-icon-on,
.vm-toggle-icon.vm-icon-add,
.vm-toggle-icon.vm-icon-remove {
	display: none;
}

.vm-toggle-button {
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	border: 0;
	box-sizing: border-box;
	border-radius: inherit;
	padding: 0;
	font-weight: bold;
}

.vm-float-map-select-button:focus-visible,
.vm-toggle-button:focus-visible {
	outline: var(--outline-width) solid var(--color-toggle);
	outline-offset: var(--outline-offset);
}
.vm-toggle-button[aria-pressed="true"]:focus-visible {
	outline-color: var(--color-toggle-inverse);
	outline-offset: calc(2 * var(--outline-offset));
}

.vm-toggle-button:active {
	background: var(--color-text);
	color: var(--color-background);
}

.vm-toggle-button[aria-pressed="true"] .vm-icon-remove.binary,
.vm-toggle-button[aria-checked="true"] .vm-icon-remove.binary,
.vm-toggle-button[aria-pressed="true"] .vm-icon-on:not(.binary),
.vm-toggle-button[aria-checked="true"] .vm-icon-on:not(.binary) {
	display: block;
}
.vm-toggle-button:not([aria-pressed], [aria-checked]) .vm-icon-add,
.vm-toggle-button[aria-pressed="false"] .vm-icon-add,
.vm-toggle-button[aria-checked="false"] .vm-icon-add {
	display: block;
}

.vm-toggle-button[aria-pressed="true"],
.vm-toggle-button[aria-checked="true"] {
	background: var(--color-toggle);
	color: var(--color-toggle-inverse);
}
.vm-toggle-button:not([aria-pressed], [aria-checked]),
.vm-toggle-button[aria-pressed="false"],
.vm-toggle-button[aria-checked="false"] {
	background: var(--color-toggle-inverse);
	color: var(--color-toggle);
}
.vm-toggle-button:not([aria-pressed], [aria-checked]) .vm-toggle-icon,
.vm-toggle-button[aria-pressed="false"] .vm-toggle-icon,
.vm-toggle-button[aria-checked="false"] {
	color: var(--color-toggle-semi);
}

@media (hover: hover) and (pointer: fine) {
	.vm-toggle-button[aria-pressed="true"]:hover .vm-icon-on,
	.vm-toggle-button[aria-checked="true"]:hover .vm-icon-on {
		display: none;
	}
	.vm-toggle-button[aria-pressed="true"]:hover .vm-icon-remove,
	.vm-toggle-button[aria-checked="true"]:hover .vm-icon-remove {
		display: block;
	}
	.vm-toggle-button:not([aria-pressed], [aria-checked]):hover .vm-icon-add,
	.vm-toggle-button[aria-pressed="false"]:hover .vm-icon-add,
	.vm-toggle-button[aria-checked="false"]:hover .vm-icon-add {
		display: block;
	}

	.vm-toggle-button:hover {
		background: var(--color-toggle-semi);
		color: var(--color-toggle-inverse);
	}
	.vm-toggle-button:not([aria-pressed], [aria-checked]):hover .vm-toggle-icon,
	.vm-toggle-button[aria-pressed="false"]:hover .vm-toggle-icon,
	.vm-toggle-button[aria-checked="false"]:hover .vm-toggle-icon {
		color: var(--color-toggle-inverse);
	}

	.vm-toggle-button:active {
		background: var(--color-toggle);
		color: var(--color-toggle-inverse);
	}
}

.vm-toggle-button > * + * {
	margin-left: var(--inline-spacing);
}

.vm-toggle-button.flip svg {
	transform: rotate(180deg);
}

.vm-close-button {
	--color-toggle: var(--color-primary);
	--color-toggle-inverse: var(--color-background);
	height: 2.25rem;
	width: 2.25rem;
	border-radius: 0.375em;
	padding: 0.55rem;
}

.vm-close-button svg {
	height: 100%;
	width: 100%;
}

.vm-label-button {
	cursor: pointer;
	color: var(--color-primary);
	background: none;
	border: none;
	padding: 0;
	outline: none;
}

.vm-label-button:focus-visible {
	outline: var(--outline-width) solid var(--color-primary);
	color: rgba(var(--color-primary-rgb), 0.7);
	text-decoration-line: underline;
}

@media (hover: hover) and (pointer: fine) {
	.vm-label-button:hover {
		color: rgba(var(--color-primary-rgb), 0.7);
		text-decoration-line: underline;
	}
}

.vm-float-button {
	color: inherit;
	padding: var(--panel-spacing);
	cursor: pointer;
	border: none;
	background: transparent;
}

.vm-float-button > .same-width {
	height: 0;
	overflow: hidden;
	margin: 0;
}

.vm-float-button > .same-width.current {
	height: unset;
}

.vm-list-expand .vm-toggle-button {
	flex-direction: column;
}

.vm-float-map-select button {
	width: 100%;
}

/* Not a toggle button to not inherit hover, aria-pressed style */
.vm-float-map-select-button {
	background: var(--color-toggle-inverse);
	color: var(--color-toggle);
	padding: 0 var(--panel-spacing);
	font-weight: bold;
	height: 3em;
	display: flex;
	align-items: center;
	cursor: pointer;
	border: 0;
	box-sizing: border-box;
	border-radius: inherit;
}

.vm-float-map-select-button[aria-pressed="true"] + div {
	height: 100%;
}

.vm-float-map-select-button[aria-pressed="false"] + div {
	height: 0;
	visibility: hidden;
}

.vm-float-map-select .vm-float-map-select-item {
	max-width: 12em;
	padding: calc(var(--panel-inner-half-spacing) / 2) var(--panel-spacing);
}

.vm-float-map-select .vm-float-map-select-item > span.vm-line-clamp {
	--line-clamp: 2;
}

.vm-float-map-select .vm-float-map-select-item > * + span {
	margin-left: var(--inline-spacing);
}

.vm-float-map-select .vm-float-map-select-item:last-child {
	padding-bottom: var(--panel-spacing);
}

.vm-float-map-select .vm-float-map-select-item:focus-visible:last-child {
	border-radius: 0 0 0.375em 0.375em;
}

.vm-float-map-select-items {
	overflow: auto;
	max-height: min(75vh, calc(100vh - 7.5em - 3 * var(--panel-spacing)));
	max-height: min(75dvh, calc(100dvh - 7.5em - 3 * var(--panel-spacing)));
}

.vm-float-map-select-items > div {
	padding-bottom: var(--panel-spacing);
}

.vm-float-map-select-icon {
	width: 2.5em;
	height: 2.5em;
	background: var(--color-neutral-mid);
}

.vm-float-map-select-item {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	background: transparent;
	font-weight: normal;
	text-align: left;
}

.vm-float-map-select-item > span.vm-line-clamp {
	--line-height: 1.15;
	--line-clamp: 3;
}

.vm-float-container.overlay .vm-float-map-select-item {
	border: 0.125em solid transparent;
	padding: var(--panel-spacing);
}

.vm-float-map-select-item:disabled {
	text-shadow: 0.05em 0 var(--color-toggle);
	color: var(--color-primary);
	background: var(--color-toggle-inverse);
	cursor: auto;
}

.vm-float-container.overlay .vm-float-map-select-item:disabled {
	border-color: var(--color-primary);
}

.vm-float-container.overlay .vm-float-map-select-icon {
	width: 4em;
	height: 4em;
}

.vm-float-map-select-icon-wrapper {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	margin: 0;
	line-height: 0;
}

.vm-float-container-bottom {
	max-width: var(--panel-main-max);
	width: 100%;
	display: flex;
	flex-direction: column-reverse;
	flex: 1;
	min-height: 0;
}

.vm-float-container-bottom > * + * {
	margin-bottom: var(--panel-spacing);
}

.vm-float-container-top {
	display: flex;
	flex-direction: row;
	min-height: 0;
	gap: var(--panel-spacing);
}

.vm-float-container-top > .vm-float-container-side {
	margin-left: auto;
}

.vm-interaction {
	/* position: relative - make this happen but fix overlapping shadows */
	display: flex;
	flex-direction: column;
	width: 100%;
	flex-shrink: 0;
	min-height: 3em;
	max-width: var(--panel-main-max);
	height: max-content;
	min-width: 3em;
	max-height: calc(50vh - 2 * var(--panel-spacing));
	max-height: calc(50dvh - 2 * var(--panel-spacing));
}

.vm-primary-button,
.vm-interaction-button {
	padding: 0.5rem;
	border-radius: 0.375em;
	--color-toggle: var(--color-primary);
	--color-toggle-inverse: var(--color-background);
}

.vm-interaction-button svg {
	height: 1.25rem;
	width: 1.25rem;
}

.vm-menu-header {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	height: 2em;
	padding: var(--panel-inner-half-spacing);
}

.vm-menu-header > .vm-interaction-button:not(:last-child) {
	margin-right: var(--quarter-spacing);
}

.vm-menu-content {
	--border: 1px solid var(--color-neutral-mid);
	overflow-y: auto;
	background: var(--color-background);
}

.vm-float-branding {
	flex: 1;
	display: flex;
	align-items: center;
	height: 2em;
	min-width: 0;
	padding-right: var(--spacing);
}

.vm-float-branding h1 {
	margin: 0 0 0 var(--half-spacing);
	--line-height: 1.5;
	font-size: var(--text-heading-2-size);
}

.vm-float-explain-filters {
	flex-grow: 1;
	text-align: center;
}

.vm-float-block-section {
	min-height: 0;
	height: 100%;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	padding: var(--panel-inner-half-spacing) var(--panel-inner-spacing);
}

.vm-float-control-panel {
	display: flex;
	flex-direction: column-reverse;
	min-height: 3.5em;
}

.vm-float-search-bar {
	display: flex;
	align-items: center;
	padding: var(--panel-inner-half-spacing);
}
.vm-float-search-bar > *:not(:first-child) {
	margin-left: var(--panel-spacing);
}

.vm-float-search-bar button {
	height: 2.5em;
	width: 2.5em;
	border-radius: 0.2em;
	--color-toggle: var(--color-primary);
	--color-toggle-inverse: var(--color-background);
	flex-shrink: 0;
}

.vm-float-search-bar button svg {
	width: 1.5em;
	height: 1.5em;
}

.vm-search-icon {
	position: absolute;
	top: 50%;
	left: 0.7em;
	transform: translateY(-50%);
	width: 0.8em;
	height: 0.8em;
	pointer-events: none;
}

.vm-float-search-bar .vm-clearable-input-container input:not(:focus):placeholder-shown ~ .vm-search-icon {
	color: var(--color-placeholder);
}

.vm-float-search-bar .vm-clearable-input-container > input {
	background: var(--color-background);
	border: 0;
	border-radius: 0.2em;
	height: 2.5em;
	box-sizing: border-box;
	-webkit-appearance: none;
	appearance: none;
	padding: 0.5em 2em;
	width: 100%;
}

.vm-float-search-bar .vm-clearable-input-container > input:focus-visible {
	outline: 0.0675em solid var(--color-primary);
	outline-offset: -0.0675em;
}

.vm-float-container.overlay {
	z-index: 1;
}

.vm-float-container.overlay > section {
	position: absolute;
	bottom: 0;
	padding: var(--panel-inner-half-spacing) var(--panel-inner-spacing);
	transition: transform var(--anim-duration) ease;
	width: 100%;
	max-width: var(--panel-main-max);
	max-height: 100%;
	display: flex;
	flex-direction: column;
}

.vm-learn {
	margin: var(--panel-inner-spacing) 0;
}

.vm-learn > h4 {
	font-size: 1em;
	font-weight: normal;
	margin: 0 var(--spacing);
}

.vm-h {
	font-family: var(--font-head);
}

.vm-h ul,
.vm-h ol {
	list-style-type: none;
	margin: 0 var(--spacing) var(--spacing) 0;
}

.vm-h h1,
.vm-h h2,
.vm-h h3,
.vm-h h4,
.vm-h h5,
.vm-h h6 {
	font-size: 1.4em;
	font-weight: normal;
	margin: var(--spacing) var(--spacing) 0 var(--spacing);
}

.vm-h details summary {
	cursor: pointer;
	background: var(--color-background);
	padding: var(--half-spacing);
	margin-bottom: var(--half-spacing);
}

.vm-h details summary > * {
	display: inline;
}

.vm-group > h4 {
	color: var(--color-text);
	font-size: 1em;
	font-weight: normal;
	margin: 0 var(--spacing) 0 var(--gap);
}

ul.vm-group {
	list-style-type: none;
	padding: 0;
	margin: 0;
}

.vm-label {
	position: relative;
}

.vm-label > a.overlay {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
}

.vm-label > div {
	position: relative;
	pointer-events: none;
	display: flex;
	flex: 1;
	flex-wrap: wrap;
	padding: 0 var(--spacing) 0 calc(var(--gap) - var(--highlight-border));
}

.vm-label > div a {
	pointer-events: all;
}

.vm-label > p {
	width: auto;
	padding: 0 var(--spacing) 0 var(--gap);
}

.vm-explain .vm-i,
.vm-label-tag .vm-i,
.vm-label .vm-i,
.vm-d .vm-i {
	font-weight: normal;
	font-style: normal;
	display: inline-block;
	height: 1.2em;
	width: 1.2em;
	vertical-align: sub;
	/* Fixes smearing on Chromium when img is an svg */
	backface-visibility: hidden;
}

.vm-d .vm-i {
	margin-left: 0.5em;
}

.vm-explain span.vm-i,
.vm-label-tag span.vm-i,
.vm-label span.vm-i,
.vm-d span.vm-i {
	text-align: center;
	vertical-align: revert;
}

.vm-label .vm-i + .vm-i,
.vm-d .vm-i + .vm-i {
	margin-left: 0;
}

.vm-object-link.offset {
	padding-left: calc(1.2em + var(--inline-spacing));
}

.vm-i.offset {
	margin-left: calc(-1 * (1.2em + var(--inline-spacing)));
	margin-right: var(--inline-spacing);
}

.vm-label span.annotations {
	font-weight: initial;
	font-family: var(--font-body);
	white-space: nowrap;
	margin-left: 0;
	display: flex;
}

.vm-label span.annotations > * + * {
	margin-left: var(--inline-spacing);
}

.vm-label span.annotations svg {
	height: 1em;
	vertical-align: middle;
}

.vm-label h4 {
	flex: 1;
	font-size: 1em;
	font-weight: normal;
	margin: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.vm-thing .vm-label h4:focus {
	color: black;
}

@media (hover: hover) and (pointer: fine) {
	.vm-thing .vm-label h4:hover {
		color: black;
	}
}

.vm-trust-expanded > div > div {
	margin-left: var(--gap);
}

.vm-trust-expanded > div {
	margin-bottom: var(--half-spacing);
}

.vm-trust-attitude {
	color: var(--color-neutral-dark);
}

.vm-trust-attitude.negative {
	color: var(--color-rag-negative);
}

.vm-trust-attitude.neutral {
	color: var(--color-rag-neutral);
}

.vm-trust-attitude.positive {
	color: var(--color-rag-positive);
}

.vm-item-list {
	overflow: hidden;
	height: auto;
	padding-bottom: var(--spacing);
}

.vm-trust-list {
	list-style: none;
}

.vm-item-list:focus {
	outline: none;
}

.vm-trust-list li > .interactable {
	cursor: pointer;
}

.vm-thing.vm-prominent {
	padding-top: var(--spacing);
}

.vm-item-list > div > .vm-thing:first-child {
	margin-top: var(--spacing);
}

.vm-group > h4.vm-prominent,
.vm-prominent .vm-label h4 {
	font-weight: bold;
}

.vm-learn > .vm-h a[aria-current="page"],
.vm-learn > h4[aria-current="page"],
.vm-thing[aria-current="location"] .vm-label h4 {
	color: var(--color-text);
	font-weight: bold;
	overflow: initial;
	white-space: normal;
}

.vm-learn > .vm-h a,
.vm-learn > h4 a {
	display: inline-block;
	width: 100%;
}

.vm-learn > .vm-h a[aria-current="page"],
.vm-learn > h4[aria-current="page"] {
	background-color: rgba(var(--color-secondary-rgb), 0.15);
}

.vm-learn > .vm-search-select {
	margin: 0 var(--spacing) var(--spacing) var(--spacing);
}

.vm-thing .vm-label > div {
	border-left: var(--highlight-border) solid transparent;
	align-items: flex-start;
}

.vm-item-list .vm-thing[aria-current="location"] .vm-label > div,
.vm-full-items .vm-thing[aria-current="location"] .vm-label > div {
	color: var(--color-text);
	border-left-color: var(--color-highlight);
}

.vm-item-list .vm-thing[aria-current="location"] .vm-label > div {
	background-color: var(--color-highlight-bg);
}

.vm-inset {
	margin: 0;
	width: 100%;
}

.vm-thing .vm-c,
.vm-thing .vm-d,
.vm-thing .vm-media {
	margin-left: var(--gap);
	margin-bottom: var(--half-spacing);
}

.vm-thing p,
.vm-thing dl,
.vm-thing ol,
.vm-thing ul,
.vm-thing h5 {
	margin: 0;
}

.vm-thing ul {
	list-style-type: none;
	padding: 0;
}

.vm-d p {
	margin: 0 var(--spacing);
}

/* Hacks */

.vm-full-items .vm-thing[aria-current="location"] {
	background-color: var(--color-highlight-bg);
}

.vm-full-items.expand .vm-thing::after {
	content: '';
	display: block;
	height: 0.0625em;
	margin: var(--spacing) var(--half-spacing) 0;
	background-color: var(--color-primary);
}

.pure-button {
	font-size: 1.5em;
	line-height: 1;
	padding: 5px;
}

.pure-button img {
	display: block;
	height: 1em;
}

/*
 * Styles for marker mess, needs reworking towards sanity later.
 */

.vm-marker {
	position: absolute;
	user-select: none;
	left: 0;
	top: 0;
	width: 40px;
	height: 40px;
	transform:
		translateX(calc(var(--markerX) * var(--layerScale) + var(--layerX) - 50%))
		translateY(calc(var(--markerY) * var(--layerScale) + var(--layerY) - 50%));
}

.vm-marker[data-iri] {
	cursor: pointer;
}

.vm-marker[data-votes]::after {
	content: attr(data-votes);
	display: block;
	pointer-events: none;
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	text-align: center;
	font-size: 34px;
	font-weight: bold;
	line-height: 40px;
	background: var(--color-white-translucent);
	border-radius: 50%;
	letter-spacing: -4px;
	text-indent: -4px;
}

.vm-mid .vm-marker[data-votes]::after {
	font-size: 24px;
	line-height: 30px;
}

.vm-far .vm-marker[data-votes]::after {
	font-size: 16px;
	line-height: 20px;
}

.vm-mid .vm-marker {
	width: 30px;
	height: 30px;
}

.vm-far .vm-marker {
	width: 20px;
	height: 20px;
}

.vm-marker-icon-container,
.vm-marker-icon {
	display: block;
	width: 100%;
	height: 100%;
}

.vm-marker-icon-container {
	position: relative;
	filter: drop-shadow(-3px 3px 2px var(--color-black-translucent));
}

.vm-marker-icon {
	background-size: contain;
	background-repeat: no-repeat;
}

.vm-marker p {
	position: absolute;
	top: 0;
	margin: 2px 44px;
	width: 112px;
	padding: 6px;
	border: 1px solid var(--color-black-semi-translucent);
	border-radius: 6px;
	box-shadow: -3px 3px 2px var(--color-black-translucent);
	background: var(--color-white-semi-opaque);
	overflow-wrap: break-word;
}

.vm-marker.left p {
	left: -174px;
	text-align: right;
}

.vm-marker.none p {
	display: none;
}

.vm-marker-aggregation:focus,
.vm-marker.focused,
.vm-marker:focus {
	z-index: 1;
}

.vm-marker-aggregation:focus,
.vm-marker:focus p {
	background: white;
}

@media (hover: hover) and (pointer: fine) {
	.vm-marker-aggregation:hover,
	.vm-marker:hover {
		z-index: 1;
	}

	.vm-marker-aggregation:hover,
	.vm-marker:hover p {
		background: white;
	}
}

.vm-marker.faded .vm-marker-icon,
.vm-marker.faded > p > span {
	filter: grayscale(1);
}

.vm-mid .vm-marker p {
	margin: 1px 34px;
	font-size: 0.75em;
	width: 84px;
}

.vm-mid .vm-marker.left p {
	left: -134px;
	text-align: right;
}

.vm-far .vm-marker p {
	display: none;
}

.vm-many .vm-far .vm-marker {
	display: none;
}

.vm-marker.focused {
	display: block !important;
}

.vm-marker-aggregation {
	position: absolute;
	display: flex;
	align-items: center;
	user-select: none;
	left: 0;
	top: 0;
	height: 30px;
	transform:
		translateX(calc(var(--markerX) * var(--layerScale) + var(--layerX) - 50%))
		translateY(calc(var(--markerY) * var(--layerScale) + var(--layerY) - 50%));
	padding: 6px;
	border: 1px solid var(--color-black-semi-translucent);
	border-radius: 6px;
	background: var(--color-white-semi-opaque);
}

.vm-marker-aggregation .vm-marker-icon {
	height: 30px;
	width: 30px;
	margin-right: 6px;
}

.vm-marker-aggregation p {
	margin: 0;
}

.vm-story-controls {
	display: flex;
	align-items: center;
	flex: 1;
	margin-bottom: var(--panel-inner-half-spacing);
	padding: var(--panel-inner-half-spacing);
}

.vm-story-content {
	padding: 0 var(--panel-inner-half-spacing);
	margin-bottom: var(--panel-inner-half-spacing);
}

.vm-story-link {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	width: 2.25em;
	height: 2.25em;
	border-radius: 50%;
	--color-toggle-inverse: var(--color-background);
}

a[aria-disabled="true"].vm-toggle-button,
.ol-zoom-in:disabled,
.ol-zoom-out:disabled,
.vm-filter-restore-button:disabled,
.vm-float-selected-item .vm-toggle-button:disabled,
.vm-pagination .vm-toggle-button:disabled,
.vm-intro-tooltip-arrows-progress .vm-toggle-button:disabled {
	pointer-events: none;
	cursor: default;
	color: rgba(var(--color-primary-rgb), 0.5) !important;
	background: rgba(var(--color-background-rgb), 0.5) !important;
	filter: grayscale(1);
}

.vm-story-link svg {
	color: inherit;
	width: 1.25em;
}

.vm-story-link.flip svg {
	transform: rotate(180deg);
}

.vm-focus-control-select {
	position: relative;
	display: flex;
	min-width: 0;
}

.vm-focus-select {
	color: var(--color-primary);
	position: absolute;
	bottom: calc(100% + 0.25em);
	right: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-shadow: 0 0.25em 0.75em rgba(49, 49, 104, 0.4);
	border-radius: 0.25em;
}

.vm-focus-select > .vm-focus-button.vm-toggle-button {
	width: 100%;
	position: relative;
	display: flex;
	align-items: center;
	font-weight: bold;
	justify-content: flex-start;
	border-radius: unset;
}

.vm-focus-select > .vm-focus-button:not(:first-child):before {
	content: "";
	position: absolute;
	width: calc(100% - 1em);
	top: 0;
	left: 0.5em;
	border-top: 0.0625rem solid rgba(var(--color-primary-rgb), 0.2);
}

.vm-focus-control-select > .vm-focus-button:not(.expand) {
	min-width: 0;
}

.vm-focus-control-select > .vm-focus-button:first-child {
	border-top-left-radius: 0.25em;
	border-bottom-left-radius: 0.25em;
}

.vm-focus-control-select > .vm-focus-button:last-child {
	border-top-right-radius: 0.25em;
	border-bottom-right-radius: 0.25em;
}

.vm-focus-select > .vm-focus-button:first-child {
	border-top-left-radius: 0.25em;
	border-top-right-radius: 0.25em;
}

.vm-focus-select > .vm-focus-button:last-child {
	border-bottom-right-radius: 0.25em;
	border-bottom-left-radius: 0.25em;
}

div.vm-focus-button {
	height: 2em;
	font-weight: bold;
	color: var(--color-primary);
	background-color: var(--color-background);
	user-select: none;
	padding-left: 0.5em;
}

.vm-focus-button > span {
	font-size: var(--text-sub-1-size);
}

.vm-focus-button span {
	display: inline-block;
	max-width: 7rem;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.vm-focus-button svg {
	width: 1.5rem;
	height: 1.5rem;
}

.vm-focus-button.vm-toggle-button {
	border-radius: 0;
	width: auto;
}

.vm-focus-button {
	padding-left: 0.25em;
	padding-right: 0.5em;
	display: flex;
	align-items: center;
}

.vm-focus-button.expand {
	filter: brightness(0.9);
	padding-right: 0.25em;
}

.vm-focus-button.expand::before {
	content: '\25b4';
	padding: 0.125em;
}

.vm-focus-button.expand[aria-expanded="true"]::before {
	content: '\25be';
}

.vm-focus-button.expand > span {
	font-size: 1.25em;
}

.vm-story-progress {
	height: 0.625em;
	width: 100%;
	background: var(--color-background);
	border-radius: 5em;
	overflow: hidden;
	margin: 0 0.625em;
}

.vm-story-progress > div {
	background: var(--color-primary-transparent);
	transition: width 0.333s ease-in-out;
	height: 100%;
}

.vm-float-block-content {
	/*unit used only for scrolling*/
	max-height: 100vh;
	overflow: auto;
	min-height: 0;
	transition: max-height 0.333s ease-in-out;

	--collapsed-lines: 2;
	--collapsed-line-height: 1.5;
	--collapsed-font-size: 1em;
	--collapsed-max-height: calc(var(--collapsed-lines) * var(--collapsed-line-height) * var(--collapsed-font-size));
}

.vm-float-block-content.collapse {
	max-height: var(--collapsed-max-height);
	/*workaround for delay of max-height: 50vh -> 0 due to content being short*/
	transition: max-height 0.222s cubic-bezier(0, 1, 0, 1);
}

.vm-float-block-content:not(.collapse) {
	animation: 0.666s show-overflow;
	animation-timing-function: steps(2);
}

@keyframes show-overflow {
	from { overflow: hidden; }
}

.vm-story-content.vm-float-block-content p {
	margin-top: 0.25em;
	margin-bottom: 0.5em;
}

.vm-story-content.vm-float-block-content.collapse {
	overflow: hidden;
	--collapsed-lines: 3;
}

.vm-float-block .vm-expand-controls {
	position: sticky;
	bottom: -1px;
	background-image: linear-gradient(to top, rgba(255,255,255,1) 50%, rgba(255,255,255,0));
	height: 2.5em;
	display: flex;
	justify-content: center;
	align-items: flex-end;
	flex-shrink: 0;
}

.vm-float-block .vm-expand-controls button {
	margin-bottom: 2px;
}

.vm-expand-button {
	color: var(--color-text);
	cursor: pointer;
	display: flex;
	justify-content: center;
	background: transparent;
	border: none;
	padding: 0;
}

.vm-expand-button svg {
	color: inherit;
	width: 1.25em;
}

.vm-float-map-select-button[aria-pressed="true"] .vm-svg:last-child,
.vm-expand-button svg,
.vm-accordion-header .vm-expand-button.flip svg {
	transform: rotate(90deg);
}

.vm-float-map-select-button .vm-svg:last-child,
.vm-accordion-header .vm-expand-button svg,
.vm-expand-button.flip svg {
	transform: rotate(-90deg);
}

.vm-accordion-header {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	cursor: pointer;
}

.vm-accordion-header.inactive {
	cursor: auto;
	pointer-events: none;
}

.vm-accordion-header .vm-expand-button {
	margin-left: 0.375rem;
}

.vm-float-map-select-button .vm-svg:last-child,
.vm-accordion-header .vm-expand-button svg {
	height: 0.75rem;
	width: 0.75rem;
}

.vm-float-map-select-button .vm-svg:first-child {
	margin: 0 0.75rem 0 0.375rem;
}

.vm-float-map-select-button .vm-svg:last-child {
	margin: 0 0.375rem 0 auto;
}

.vm-float-map-select-button .vm-svg:first-child + span {
	margin-right: 0.375rem;
}

.vm-accordion .vm-accordion-content-container {
	overflow: hidden;
	transition: max-height 0.333s ease-in-out;
}

.delayed-hide {
	visibility: hidden;
	animation: 0.333s always-visible;
	animation-timing-function: steps(2);
}

@keyframes always-visible {
	from { visibility: visible; }
}
.vm-voting {
	overflow: auto;
}

dialog {
	display: none;
	border: 0;
	outline: 0;
}

dialog[open] {
	display: flex;
	flex-direction: column;
}

dialog[open]::backdrop {
	background: white;
	opacity: 0.7;
}

.vm-splash {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2;
}

.vm-splash > div {
	position: absolute;
	width: 100%;
	height: 100%;
	background: white;
	opacity: 0.7;
}

.vm-splash > section {
	width: 100%;
	display: flex;
	flex-direction: column;
	max-height: 70%;
	background-color: white;
	border-radius: 0.375em;
	box-shadow: 0 0.25em 1em rgba(49, 49, 104, 0.4);
	padding: var(--panel-inner-half-spacing) var(--panel-inner-spacing);
	padding-bottom: 0;
	z-index: 1;
}

.vm-splash > section:focus-visible {
	outline: none;
}

.vm-splash > section > .contents {
	overflow-y: auto;
	flex: 1;
}

.vm-splash > section h4 {
	font-weight: 700;
	font-size: var(--text-heading-1-size);
	line-height: 1.1;
	margin: var(--panel-inner-spacing);
	cursor: pointer;
}

.vm-splash > section .vm-c {
	background: var(--color-background);
	padding: var(--panel-inner-half-spacing) 0;
	border-radius: 0.375em;
}

.vm-splash .vm-logo {
	display: flex;
	justify-content: center;
	margin-top: var(--panel-inner-half-spacing);
}

.vm-splash .vm-logo svg{
	width: 30%;
}

.vm-build-info {
	color: rgba(var(--color-neutral-mid-rgb), 0.5);
	text-align: right;
}

.vm-license {
	background: white;
	font-size: 0.8em;
	margin: var(--spacing);
	max-height: 22em;
	padding-top: var(--spacing);
	overflow: auto;
}

.vm-aside {
	color: var(--color-neutral-mid);
	margin: var(--gap);
}

.vm-icon,
.vm-icon:link,
.vm-icon:active,
.vm-icon:visited {
	border: 2px white solid;
	box-sizing: border-box;
	padding: 0;
	background: none;
	display: block;
	flex-shrink: 0;
	font-size: 0;
	width: var(--spacing);
	height: var(--spacing);
}

.vm-icon[aria-pressed="true"],
.vm-icon[aria-pressed="false"] {
	cursor: pointer;
}

.vm-icon:disabled {
	filter: grayscale(80%);
}

.vm-icon.info {
	background: center / contain var(--color-primary) no-repeat var(--icon-info);
}

.vm-icon.more {
	background: center / contain var(--color-primary) no-repeat var(--icon-more);
}

.vm-icon.more[aria-pressed="true"] {
	background: center / contain var(--color-primary) no-repeat var(--icon-less);
}

.vm-icon.up,
.vm-icon.down[aria-pressed="true"] {
	background: center / contain var(--color-primary) no-repeat var(--icon-less);
	transform: rotate(90deg);
}

.vm-icon.down,
.vm-icon.up[aria-pressed="true"] {
	background: center / contain var(--color-primary) no-repeat var(--icon-less);
	transform: rotate(-90deg);
}

.vm-i.nav-up {
	background: center / contain transparent no-repeat var(--icon-nav-up);
}

.vm-labels > div {
	position: absolute;
}

.vm-map-label {
	opacity: .9;
}

.vm-map-label[href] {
	cursor: pointer;
	outline: none;
}

.vm-map-label.inlaid {
	opacity: 1;
}

.vm-map-label.absent {
	opacity: .5;
}

.vm-map-label.selected {
	stroke: var(--color-label-border);
	opacity: 1;
}

@media (hover: hover) and (pointer: fine) {
	.vm-map-label:hover:not(.invalid) {
		opacity: 1;
	}
}

.vm-map-label > .shadow,
.vm-map-label > .content {
	transition: transform 0.12s ease 0.08s, opacity 0.12s ease 0.08s;
}

.vm-map-label > .shadow {
	fill: var(--color-black-semi-translucent);
	filter: url(#label-shadow-blur);
	opacity: 0;
}

@media (hover: hover) and (pointer: fine) {
	.vm-map-label:not(:focus):not(:active):not(.inlaid):not(.invalid):not(.selected):hover > .content {
		transform: translate(0, calc(-0.5 * var(--label-hover-offset)));
	}

	.vm-map-label:not(:focus):not(:active):not(.inlaid):not(.invalid):not(.selected):hover > .shadow {
		opacity: 1;
		transform: translate(calc(-1 * var(--label-hover-offset)), calc(0.5 * var(--label-hover-offset)));
	}
}

.vm-map-label.invalid {
	stroke: var(--color-invalid);
}

.vm-map-label.faded {
	filter: url(#label-grayscale);
}

.vm-filter-controls .collapse {
	display: none;
}

.label-bar {
	display: flex;
	align-items: center;
}

.label-bar label {
	flex: 1;
}

.label-bar svg {
	width: 1rem;
	height: 1rem;
	color: black;
}

.vm-filter-controls fieldset .label-bar button {
	background-color: transparent;
	margin-left: 5px;
}

.vm-filter-controls fieldset {
	text-align: start;
}

.vm-filter-controls .vm-icon.up {
	display: inline-block;
	margin: calc(var(--spacing) / 2);
}

.vm-filter-list {
	--border: 1px solid var(--color-neutral-mid);
	margin: 0;
	padding: 0;
	background-color: white;
	height: 100%;
	display: table;
	border-collapse: collapse;
	border-spacing: 0;
	line-height: 1.15;
}

.vm-filter-list li {
	display: table-row;
	height: 100%;
	width: calc(100% - 2px);
	border: var(--border);
}

.vm-filter-list li > span {
	padding: 5px 10px;
	display: table-cell;
	width: 100%;
	border: var(--border);
	border-right: none;
	vertical-align: middle;
}

.vm-filter-list button.text-red,
.vm-filter-list button.text-red:active {
	color: red;
}

.vm-filter-list button.text-green,
.vm-filter-list button.text-green:active {
	color: green;
}

.vm-filter-controls .pure-form button {
	background-color: white;
	border: none;
	text-align: center;
	text-decoration: none;
	cursor: pointer;
	padding: 5px 10px;
	outline: none;
	display: table-cell;
	height: 100%;
	word-break: keep-all;
}

.vm-filter-controls .pure-form button > span {
	/* affects height */
	overflow: auto;
}

.vm-filter-controls .pure-form button:active {
	/* Needed for Safari */
	color: var(--color-text);
}

.vm-filter-controls .pure-form button:focus:not(:active) {
	outline: 1px auto var(--color-primary);
}

.vm-filter-list button:last-of-type:not(:nth-child(1)) {
	margin-left: auto;
}

.vm-input-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.vm-input-row label {
	flex-shrink: 0;
}

.vm-input-row input,
.vm-input-row select {
	flex-grow: 1;
	margin-left: var(--half-spacing);
	min-width: 0;
	width: 100%;
	text-overflow: ellipsis;
}

.vm-dropdown-container {
	position: relative;
}

.vm-dropdown-list {
	background-color: white;
	list-style-type: none;
	margin: 0;
	padding: 0;
	max-height: 8em;
	overflow-y: auto;
	border: 1px solid var(--color-neutral-dark);
	width: 80%;
	z-index: 1;
}

.vm-dropdown-list.up {
	transform: translateY(calc(-100% - 2rem));
}

.vm-dropdown-list.expand {
	position: absolute;
}

.vm-filter-controls .vm-dropdown-list button {
	width: 100%;
	text-align: left;
	padding: 5px 10px;
	border-bottom: 1px solid var(--color-neutral-mid);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.vm-items-per-page select,
.vm-search-select-area {
	background-color: white;
	color: var(--color-primary);
	border: 1px solid #ccc;
	box-shadow: inset 0 1px 3px #ddd;
	border-radius: 4px;
	position: relative;
	font-weight: bold;
	padding: var(--quarter-spacing) 0 var(--quarter-spacing) 0;
	cursor: text;
}

.vm-items-per-page select {
	background-color: var(--color-background);
	margin-left: 0.75em;
}

@media (max-width: 55.999em) {
	.vm-items-per-page select {
		margin-left: 0.25em;
	}
}

.vm-search-select-area.active {
	border-color: #129fea;
	padding: var(--quarter-spacing) 0 0 0;
}

.vm-search-select-area input[type=search],
.vm-search-select-area input[type=search]:focus,
.vm-search-select-area input[type=search]:focus-visible {
	border: none;
	outline: none;
	position: absolute;
	z-index: 2;
	width: 100%;
	height: 2.375em;
	background: none;
	left: 0;
	top: 0;
	padding: var(--quarter-spacing) var(--gap) var(--quarter-spacing) var(--half-spacing);
}

.vm-search-select-area.active input[type=search] {
	border-bottom: var(--border);
}

.vm-search-select-area .text.light {
	color: var(--color-label-border);
}

.vm-search-select-area > .text {
	position: relative;
	z-index: 1;
	display: block;
	padding-left: var(--half-spacing);
	padding-right: var(--spacing);
	line-height: 1.625;
	height: 1.625em;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.vm-search-select-area > .hide.text {
	visibility: hidden;
}

.vm-search-select-area > .vm-expand-button {
	cursor: pointer;
	float: right;
	position: absolute;
	z-index: 3;
	right: 0;
	top: 0;
	margin-left: 0;
	padding: var(--half-spacing);
}

.vm-search-select-area .vm-expand-button > svg {
	color: var(--color-primary);
	transform: rotate(90deg);
	width: 0.75em;
	margin-left: auto;
	transition: transform var(--anim-duration) ease;
}

.vm-search-select-area .vm-expand-button.flip > svg {
	transform: rotate(-90deg);
}

.vm-search-select-dropdown {
	width: 100%;
	padding: 0;
}

.vm-search-select-dropdown.active {
	padding-top: var(--quarter-spacing);
}

.vm-search-select-dropdown .vm-dropdown-list {
	width: 100%;
	border: none;
	margin-bottom: 0;
	overflow-x: hidden;
	transition: height var(--anim-duration) ease;
	border-radius: 0 0 0.375em 0.375em;
}

.vm-search-select-dropdown .message {
	padding-left: var(--half-spacing);
	color: rgba(0,0,0,.4);
}

.vm-search-select-dropdown .message.hidden {
	display: none;
}

.vm-search-select-dropdown .vm-dropdown-list li {
	width: 100%;
	text-align: left;
	border: None;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	color: var(--color-primary);
	cursor: pointer;
	margin-left: 0;
	padding: 0 var(--half-spacing);
}

@media (hover: hover) and (pointer: fine) {
	.vm-search-select-dropdown .vm-dropdown-list li:hover {
		color: var(--color-text);
		background: rgba(var(--color-secondary-rgb), 0.15);
	}
}

.vm-search-select-dropdown .vm-dropdown-list li[aria-current="true"] {
	color: var(--color-text);
	background: rgba(var(--color-secondary-rgb), 0.1);
}

.vm-search-select-dropdown .vm-dropdown-list li:focus-visible {
	outline: none;
}

.vm-languages,
.vm-domains {
	margin: var(--spacing);
}

.vm-domains > * + * {
	margin-top: var(--half-spacing);
}

.vm-domains select,
.vm-domains button {
	color: inherit;
	margin-left: var(--half-spacing);
}

.vm-domains button[aria-pressed="true"] {
	background: var(--color-primary);
}

.vm-domains .vm-search-select-area button[aria-pressed="true"] {
	background: none;
}

.vm-icon-flag {
	display: inline-block;
	vertical-align: middle;
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
	width: calc(4em / 3);
	height: 1em;
}

.vm-line-clamp {
	--line-clamp: 1;
	--font-size: 1em;
	line-height: var(--line-height);
	max-height: calc(var(--line-clamp) * var(--line-height) * var(--font-size));
	overflow: hidden;
}

.vm-access {
	display: none;
}

/* Debug styling */

.vm-geek-panel {
	position: absolute;
	top: 0;
	left: auto;
	bottom: 0;
	right: var(--panel-spacing);
	margin: auto 0;
	height: 12em;
	box-sizing: border-box;
	box-shadow: 0 0.25em 1em rgba(49, 49, 104, 0.4);
	border-radius: 0.375em;
	background: repeating-linear-gradient(-45deg, white, white 20px, magenta 20px, magenta 21px);
	padding: 1em;
	overflow: scroll;
}

.vm-geek-panel label {
	display: block;
}

.vm-geek-panel label input {
	display: inline-block;
	margin: 0 .5em;
}

.vm-geek-lens-container {
	position: absolute;
	bottom: 5.5em;
	right: var(--panel-spacing);
	display: flex;
	flex-direction: column;
	align-items: end;
}

.vm-geek-lens-container ul {
	list-style-type: none;
	margin: 0;
	padding: 0;
}

.vm-geek-lens-container > .vm-float-block {
	padding: var(--panel-inner-half-spacing);
}

.vm-geek-lens-container > * + * {
	margin-top: var(--panel-spacing);
}

.vm-page.debug-hide > .vm-float,
.vm-page.debug-hide > .vm-float-block {
	visibility: hidden;
}

.vm-page.debug-fade .vm-float-block {
	background: rgba(var(--color-neutral-light-rgb), 0.5);
}

.vm-page.debug-fade .vm-float-block > * {
	visibility: hidden;
}

.vm-debug-pink {
	stroke: magenta;
	stroke-width: calc(3 * var(--unscaled-width, 1));
	stroke-dasharray: calc(6 * var(--unscaled-width, 1)) calc(3 * var(--unscaled-width, 1));
}

.vm-debug-pink > .inferred {
	stroke: darkmagenta;
}

.vm-debug-pink > .selected {
	stroke-dashoffset: 0;
	animation: ants .3s infinite linear reverse;
}

@keyframes ants {
	to {
		stroke-dashoffset: calc(9 * var(--unscaled-width, 1));
	}
}


/* Media Components styling */

.vm-media {
	position: relative;
	margin-left: var(--spacing);
	margin-right: var(--spacing);
	height: 12em;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1rem;
}

.vm-media > img {
	width: 100%;
	min-height: 100%;
	max-height: 100%;
	object-fit: cover;
}

.vm-media > video {
	display: block;
	position: absolute;
	top: 0;
	width: 100%;
	height: 100%;
	object-fit: contain;
}

iframe {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

iframe:not(.loaded) {
	display: none;
}

iframe.loaded ~ span.loading {
	display: none;
}

.vm-enlarge-content-container {
	width: 100%;
}

.vm-enlarge-content {
	position: relative;
	width: 100%;
	height: 100%;
}

.vm-enlarge-controls {
	display: flex;
	justify-content: center;
	margin: var(--panel-inner-half-spacing) 0;
}

.vm-enlarge-controls button {
	border: none;
	background: none;
	cursor: pointer;
	color: var(--color-primary);
	font-weight: bold;
	line-height: 1.5;
}

.vm-enlarge-controls svg {
	height: 1.2em;
	vertical-align: sub;
	margin-left: var(--inline-spacing);
}

.vm-media-container.application > .vm-media,
.vm-media-container.video > .vm-media {
	aspect-ratio: 16 / 9;
}

.vm-media-container > .relation-group {
	margin-top: var(--panel-inner-half-spacing);
}

dialog[open].vm-media-dialog {
	padding: var(--panel-inner-half-spacing);
	font-size: 1rem;
	min-height: 0;
	max-width: calc(100% - 2 * var(--panel-spacing));
	max-height: calc(100% - 2 * var(--panel-spacing));
}

dialog[open].vm-media-dialog:not(:has(.vm-enlarge-content.image)) {
	width: calc(100% - 2 * var(--panel-spacing));
	height: 80%;
}

dialog[open].vm-media-dialog .vm-enlarge-content {
	min-height: 0;
	overflow: hidden;
}

dialog[open].vm-media-dialog .vm-media {
	min-height: 0;
	margin: 0;
	height: 100%;
}

dialog[open] .vm-media > img {
	object-fit: contain;
	max-height: calc(100vh - 2em - 5 * var(--panel-spacing));
	max-height: calc(100dvh - 2em - 5 * var(--panel-spacing));
}

/* Media Components styling - Wide/big screen layout overrides */
@media (min-width: 56em) {
	dialog[open].vm-media-dialog {
		max-height: 80%;
		max-width: 80%;
	}

	dialog[open] .vm-media img {
		max-height: calc(80vh - 2em - 3 * var(--panel-spacing));
		max-height: calc(80dvh - 2em - 3 * var(--panel-spacing));
	}
}

/* Custom SVG styling */
.vm-svg {
	--stroke-width: min(0.125em, 0.125rem);
	stroke-width: var(--stroke-width);
	flex-shrink: 0;
}

.vm-svg .cls-1 {
	fill:#349bb2;
}

.vm-svg .cls-2 {
	fill:#58595b;
}

.vm-stroke-md {
	stroke-width: var(--stroke-width);
}

.vm-stroke-sm {
	stroke-width: calc(0.75 * var(--stroke-width));
}

.vm-stroke-xsm {
	stroke-width: calc(0.5 * var(--stroke-width));
}

.vm-svg path,
.vm-svg circle {
	vector-effect: non-scaling-stroke;
}

.vm-svg.scaling-stroke path,
.vm-svg.scaling-stroke circle {
	vector-effect: unset;
}

/*
 * Scrolling text
 */

.vm-scrolling-container {
	width: 100%;
	overflow: hidden;
	white-space: nowrap;
	position: relative;
}

.vm-scrolling-text {
	--scroll-distance: 0px;
	--scroll-total-time: 0s;
	display: inline-block;
	position: relative;
	transition: transform 0.5s ease;
	animation: scrollText var(--scroll-total-time) linear infinite;
	animation-delay: 2s;
}

@media (hover: hover) and (pointer: fine) {
	.vm-scrolling-text:hover { /* reset animation */
		animation: none;
	}
}

@keyframes scrollText {
	0%, 10% { /* Pause at start */
		transform: translateX(0);
	}
	85%, 100% { /* Pause at end */
		transform: translateX(var(--scroll-distance));
	}
}

.vm-panel-header .vm-t .vm-l .title.vm-scrolling-container {
	text-overflow: clip;
}

/*
 * Scrollable container
 */

.vm-scrollable {
	position: relative;
	overflow: hidden;
	z-index: 0;
}

.vm-scrollable .vm-control-button {
	position: absolute;
	cursor: pointer;
	top: 50%;
	transform: translateY(-50%);
	height: 100%;
	border: 0;
	background: none;
	color: var(--color-primary);
	padding: var(--inline-spacing);
	z-index: 3;
	display: flex;
	align-items: center;
}

.vm-scrollable .vm-control-button:focus-visible {
	outline: var(--outline-width) solid var(--color-primary);
	outline-offset: var(--outline-offset);
}

.vm-scrollable .vm-control-button svg {
	height: 0.75em;
	width: 0.75em;
}

.vm-scrollable .vm-control-button.left {
	left: 0;
	border-radius: 0.25em 0 0 0.25em;
}

.vm-scrollable .vm-control-button.right {
	right: 0;
	border-radius: 0 0.25em 0.25em 0;
}

.vm-control-button.flip svg {
	transform: rotate(180deg);
}

.vm-scrollable-content {
	display: flex;
	overflow: scroll;
	scrollbar-width: none;
	gap: var(--panel-inner-half-spacing);
}

/* Hide scrollbar on Safari pre-18.2 and Opera */
.vm-scrollable-content::-webkit-scrollbar {
	display: none;
}

.vm-scrollable-content.left::before,
.vm-scrollable-content.right::after {
	content: "";
	position: absolute;
	top: 0;
	width: 3rem;
	height: 100%;
	pointer-events: none;
	z-index: 2;
}

.vm-scrollable-content.left::before {
	left: 0;
	background-image: linear-gradient(to right, white 50%, rgba(255, 255, 255, 0) 100%)
}

.vm-scrollable-content.right::after {
	right: 0;
	background-image: linear-gradient(to left, white 50%, rgba(255, 255, 255, 0) 100%)
}

/* Mobile overrides */
.mobile.vm-scrollable-content.left::before,
.mobile.vm-scrollable-content.right::after {
	width: 2rem;
}

.mobile.vm-scrollable-content.left::before {
	background-image: linear-gradient(to right, white 25%, rgba(255, 255, 255, 0) 100%)
}

.mobile.vm-scrollable-content.right::after {
	background-image: linear-gradient(to left, white 25%, rgba(255, 255, 255, 0) 100%)
}


/*
 * Explicit separator
 */

.vm-separator::before {
	display: block;
	content: "";
}

.vm-separator.vertical::before {
	border-left: 0.0625rem solid rgba(var(--color-text-rgb), 0.3);
	height: 100%;
}

.vm-separator.horizontal::before {
	border-top: 0.0625rem solid rgba(var(--color-text-rgb), 0.3);
	width: 100%;
}


/*
 *	Openlayers component styling overrides
 */

.ol-attribution {
	text-align: left;
	left: .5em;
	right: auto;
}

.ol-attribution.ol-uncollapsible {
	right: auto;
}

.ol-zoom {
	top: auto;
	left: auto;
	bottom: var(--panel-spacing);
	right: var(--panel-spacing);
}

.vm-page .vm-content.mode-list ~ .ol-zoom,
.vm-page .vm-content.mode-list ~ .vm-geek-lens-container {
	display: none;
}

.ol-zoom,
.ol-zoom .ol-zoom-in,
.ol-zoom .ol-zoom-out {
	border-radius: 0.25em;
}

.ol-zoom button,
.ol-zoom button:focus {
	cursor: pointer;
	background-color: white;
	border: 0.125em solid var(--color-primary);
	color: var(--color-primary);
	width: 2.25em;
	height: 2.25em;
	outline: none;
}

.ol-zoom button:focus-visible {
	outline: var(--outline-width) solid var(--color-primary);
	outline-offset: calc(-1 * var(--outline-width));
}


@media (hover: hover) and (pointer: fine) {
	.ol-zoom button:hover {
		cursor: pointer;
		background-color: var(--color-primary);
		border: 0.125em solid var(--color-primary);
		color: white;
		width: 2.25em;
		height: 2.25em;
		outline: none;
	}
}

.ol-zoom.custom {
	border-radius: 0.375em;
}

.ol-zoom.custom button {
	border: none;
	border-radius: 0.375em;
	margin: 0;
}

.ol-zoom.custom button svg {
	height: 1.25em;
	width: 1.25em;
}

.ol-zoom button:active {
	color: white;
	background-color: var(--color-text);
}

/* Small screen layout overrides */
@media (max-width: 55.999em) {
	.vm-float-container {
		right: max(var(--panel-spacing), 100vw - var(--panel-main-max) - var(--panel-spacing));
		right: max(var(--panel-spacing), 100dvw - var(--panel-main-max) - var(--panel-spacing));
	}

	.vm-float-container-bottom {
		max-height: calc(50vh - var(--panel-spacing));
		max-height: calc(50dvh - var(--panel-spacing));
	}

	.vm-float-container-bottom.keyboard-visible .vm-float-control-panel {
		position: absolute;
		max-height: calc(var(--vh)*100 - 2 * var(--panel-spacing));
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 1;
	}

	.vm-float-container-bottom.keyboard-visible .vm-float-control-panel .vm-float-block-content{
		height: calc(var(--vh) * 100);
	}

	.vm-float-container-side {
		max-height: calc(50vh - 2 * var(--panel-spacing));
		max-height: calc(50dvh - 2 * var(--panel-spacing));
		flex-shrink: 0;
		align-items: flex-end;
	}

	.vm-float-container-side > .vm-button-group {
		flex-direction: column-reverse;
		align-items: flex-end;
	}

	.vm-float-container-side > .vm-button-group > * + * {
		margin-bottom: var(--panel-spacing);
		margin-left: 0;
	}

	.vm-float-control-panel {
		max-height: 100%;
	}

	.vm-page .ol-zoom {
		display: none;
	}

	.vm-search-recent li:has(.vm-anchored-button) {
		padding-right: calc(1.8em + var(--panel-inner-spacing));
	}

	.vm-search-recent li .vm-anchored-button {
		display: flex;
	}

	.vm-search-recent li .vm-anchored-button.vm-toggle-button {
		--color-toggle-inverse: white;
	}
}

/* Wide/big screen layout overrides */
@media (min-width: 56em) {
	.vm-bar:not([aria-expanded="false"]) {
		width: var(--bar-max);
		border-right-width: 1px;
		left: 0;
	}
	.vm-bar:not([aria-expanded="false"]) > div {
		visibility: visible;
	}

	.vm-splash > section {
		width: 38em;
	}

	.vm-full-items-container {
		margin-left: max(29rem, calc(50% - 21rem));
		max-width: 42rem;
	}

	.vm-float-map-select .vm-float-map-select-icon-wrapper:before {
		content: "";
		position: absolute;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.1);
	}
}

@media (min-width: 152em) {
	html { font-size: 150%; }

	.vm-marker-aggregation,
	.vm-marker { font-size: 66.67%; }
}

@supports (-webkit-line-clamp: 1) {
	.vm-line-clamp {
		--line-clamp: 1;
		display: -webkit-box;
		line-clamp: var(--line-clamp);
		-webkit-line-clamp: var(--line-clamp);
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		word-wrap: break-word;
	}
}

/*
 * Temporary browser compat hacks
 */

/* stylelint-disable-next-line selector-type-no-unknown */
_::-webkit-full-page-media, _:future, :root .vm-filter-controls .pure-form button {
	margin-left: 2px;
	vertical-align: middle;
}

/*
 * Logout form, styled as an inline link.
 * TODO: Mostly copied from the same class in base.css, should really be some shared code.
 */

.logout-form {
	display: inline;
}

.logout-form button {
	background: none;
	border: none;
	cursor: pointer;
	padding: 0;
	color: var(--color-primary);
	text-decoration: underline;
}

@media (hover: hover) and (pointer: fine) {
	.logout-form button:hover {
		background-color: transparent;
		color: #000;
	}
}

.vm-icon-button,
.vm-filter-controls .pure-form .vm-icon-button {
	position: absolute;
	right: 0.5rem;
	top: 50%;
	transform: translateY(-50%);
	background: transparent;
	border: none;
	cursor: pointer;
	border-radius: 0.2em;
	opacity:0;
	padding:0;
	display:flex;
	align-items: center;
	justify-content: center;
	width:1.5em;
	height:1.5em;
}

.vm-search-select-area .vm-icon-button {
	top: 1.2rem;
	right: 3rem;
	z-index: 2;
}

.vm-icon-button:focus-within {
	outline: var(--outline-width) solid var(--color-toggle);
	outline-offset: var(--outline-offset);
}

@media (hover: hover) and (pointer: fine) {
	.vm-icon-button:hover,
	.vm-filter-controls .pure-form .vm-icon-button:hover {
		background-color: var(--color-primary);
	}

	.vm-icon-button:hover svg {
		color: var(--color-background);
	}
}

.vm-icon-button svg {
	color: var(--color-primary);
	width: 0.7em;
	height: 0.7em;
}

.vm-clearable-input-container:focus-within .vm-icon-button {
	opacity: 1; /* Visible on input focus */
}

@media (min-width: 56em) and (hover: hover) and (pointer: fine) {
	.vm-clearable-input-container:hover .vm-icon-button,
	.vm-search-select-area:hover .vm-icon-button {
		opacity: 1; /* Visible on input hover */
	}
}

.vm-clearable-input-container {
	position: relative;
	display: inline-block;
	width: 100%;
}

.vm-pagination {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: var(--half-spacing) var(--spacing) var(--half-spacing) var(--gap);
	box-sizing: border-box;
}

@media (max-width: 55.999em) {
	.vm-pagination {
		padding: var(--half-spacing);
	}
}

.vm-pagination-buttons-group {
	display: flex;
	align-items: center;
	gap:0.5em;
}

.vm-pagination svg,
.vm-intro-tooltip-arrows-progress svg {
	width: 1em;
}

.vm-pagination svg.flip,
.vm-intro-tooltip-arrows-progress svg.flip {
	transform: rotate(180deg);
}

/*
 * IntroTutorial Component Styles
 */

/* Main container */
.vm-intro-tutorial {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1000;
	pointer-events: none;
}

/* Welcome screen container */
.vm-intro-welcome {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: var(--color-backdrop);
	backdrop-filter: blur(3px);
	-webkit-backdrop-filter: blur(3px);
	z-index: 1000;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
	text-align: center;
	transition: opacity 0.5s cubic-bezier(0.19, 1, 0.22, 1);
}

.vm-intro-welcome-content {
	background: linear-gradient(145deg, #ffffff, #f5f5f5);
	color: #333;
	border-radius: 20px;
	box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2), 0 5px 15px rgba(0, 0, 0, 0.1);
	z-index: 1001;
	transition: all 0.4s ease-out;
}

@media (min-width: 56em) {
	.vm-intro-welcome-content {
		padding: var(--spacing);
		width: 500px;
		max-width: 500px;
	}
}

@media (max-width: 55.999em) {
	.vm-intro-welcome-content {
		padding: var(--half-spacing);
		width: calc(100% - var(--spacing));
		max-width: 300px;
	}
}

.vm-intro-welcome-title {
	margin-bottom: var(--spacing);
	color: var(--color-text);
	font-size: var(--text-heading-1-size);
}

.vm-intro-welcome-text {
	line-height: 1.6;
	color: var(--color-neutral-dark);
	margin-bottom: var(--spacing);
}

.vm-intro-welcome-buttons {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	gap: var(--half-spacing);
	margin-top: var(--half-spacing);
}

/* Confetti container */
.vm-intro-confetti-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1002;
	perspective: 700px;
}

@keyframes confetti {
	0% {
		transform: translateY(-20px) rotate(0deg);
		opacity: 1;
	}
	100% {
		transform: translateY(100vh) rotate(360deg);
		opacity: 0;
	}
}

/* Congratulations dialog */
.vm-intro-congrats {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background: white;
	color: #333;
	padding: var(--spacing) var(--gap);
	border-radius: 12px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
	z-index: 3;
	text-align: center;
	pointer-events: auto;
}

.vm-intro-congrats-title {
	margin-bottom: var(--half-spacing);
	font-size: var(--text-heading-1-size);
	color: var(--color-text);
}

.vm-intro-congrats-text {
	color: var(--color-neutral-dark);
	margin-bottom: var(--spacing);
}

.vm-intro-congrats-button-container {
	display: flex;
	justify-content: center;
	margin-top: var(--spacing);
}

/* Backdrop elements */
.vm-intro-backdrop {
	position: fixed;
	background: var(--color-backdrop);
	backdrop-filter: blur(3px);
	-webkit-backdrop-filter: blur(3px);
	pointer-events: auto;
}

.vm-intro-backdrop-congrats {
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

/* Highlighted element */
.vm-intro-highlight {
	position: absolute;
	border-radius: 8px;
	box-shadow: 0 0 0 4px rgba(var(--color-primary-rgb),0.5), 0 0 0 8px rgba(var(--color-primary-rgb),0.2);
	animation: pulse 2s infinite;
	transition: all 0.3s ease;
	pointer-events: auto;
	cursor: default;
	z-index: 1001;
}

@keyframes pulse {
	0% {
		box-shadow: 0 0 0 4px rgba(var(--color-primary-rgb),0.5), 0 0 0 8px rgba(var(--color-primary-rgb),0.2);
	}
	50% {
		box-shadow: 0 0 0 8px rgba(var(--color-primary-rgb),0.5), 0 0 0 16px rgba(var(--color-primary-rgb),0.2);
	}
	100% {
		box-shadow: 0 0 0 4px rgba(var(--color-primary-rgb),0.5), 0 0 0 8px rgba(var(--color-primary-rgb),0.2);
	}
}

/* Tooltip */
.vm-intro-tooltip {
	position: absolute;
	background: linear-gradient(145deg, #ffffff, #f5f5f5);
	color: var(--color-text);
	border-radius: 12px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
	z-index: 1001;
	pointer-events: auto;
	transition: all 0.3s ease;
}

@media (min-width: 56em) {
	.vm-intro-tooltip {
		box-sizing: border-box;
		width: 28rem;
		padding: var(--half-spacing) var(--spacing);
	}
}

@media (max-width: 55.999em) {
	.vm-intro-tooltip {
		position: fixed;
		width: calc(100% - 2 * var(--panel-inner-spacing) - 1rem);
		max-width: 320px;
		padding: var(--panel-inner-half-spacing) var(--panel-inner-spacing);
	}
}

.vm-intro-tooltip-text {
	margin: var(--spacing) 0;
	line-height: 1.6;
}

.vm-intro-tooltip-buttons {
	display: flex;
	justify-content: space-between;
	gap: var(--half-spacing);
	margin-bottom: var(--half-spacing);
}

.vm-intro-tooltip-button {
	padding: var(--quarter-spacing) var(--half-spacing);
	cursor: pointer;
	border: none;
	border-radius: 6px;
	transition: all 0.2s ease;
}

.vm-intro-tooltip-button[disabled] {
	cursor: not-allowed;
}

.vm-intro-tooltip-step {
	text-align: center;
	font-weight: 600;
	color: var(--color-neutral-dark);
	font-size: var(--text-sub-1-size);
}

.vm-intro-tooltip-arrows-progress {
	display: flex;
	align-items: center;
	gap: 1em;
	justify-content: center;
	margin-bottom: 1em;
}

.vm-intro-progress-container {
	background: var(--color-border, #e0e0e0);
	border-radius: 1em;
	height: 0.5em;
	flex-grow: 1;
	overflow: hidden;
}

.vm-intro-progress-bar {
	height: 100%;
	background: var(--color-primary-transparent);
	transition: width 0.5s ease-in-out;
	border-radius: 3px;
}

.vm-sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border: 0;
}
