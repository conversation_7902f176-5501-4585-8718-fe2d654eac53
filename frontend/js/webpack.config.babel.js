import {execSync} from 'child_process';
import {platform, release} from 'os';
import {URL} from 'url';
import webpack from 'webpack';
import {BundleAnalyzerPlugin} from 'webpack-bundle-analyzer';
import LicenseCheckerWebpackPlugin from 'license-checker-webpack-plugin';
import TerserWebpackPlugin from 'terser-webpack-plugin';
import PreactRefreshPlugin from '@prefresh/webpack';

const isWindows = platform() === 'win32' || release().includes('WSL');

export default (env) => {
  const apiUrl = env.api ?? 'http://127.0.0.1:8000/';
  const host = env.host ?? isWindows ? '127.0.0.1' : undefined;
  const port = Number(env.port) || Number(process.env.PORT) || undefined;

  const mode = process.env.NODE_ENV = env.mode ?? 'production';
  const isDev = mode === 'development';
  const watch = env.watch ?? isDev;
  const omitSourceMaps = !!env['omit-source-maps'] || !!process.env.OMIT_SOURCE_MAPS;

  const getEntry = (entryPoint) => {
    const entryPath = watch || isDev ? `./src/entry/${entryPoint}debug.tsx` : `./src/entry/${entryPoint}final.tsx`;
    return [entryPath, ...(watch ? ['webpack-dev-server/client'] : [])];
  };

  const entry = {
    basecss: './style/base.css',
    login: './scripts/login.js',
    mapterms: getEntry('mapterms'),
    viewmap: getEntry('app'),
  };

  const describeCurrentCommit = () => (
    // Heroku doesn't let us get at GIT_DIR but has got commit hash in env
    process.env.SOURCE_VERSION?.slice(0, 8)
    || execSync('git describe --always --dirty=+', {encoding: 'utf8'}).trim());

  const outputWriter = ({dependencies}) => `# Shared Meaning Platform

  Portions of the platform are free software used under the following licenses.

  ## Dependencies

  ${dependencies.map(d => `### ${d.name}
  * **Version:** ${d.version}
  * **License:** [${d.licenseName}](https://spdx.org/licenses/${d.licenseName}.html)
  ${d.repository ? `* **Repository:** ${d.repository}
  ` : ''}`,
    ).join('\n')
  }`;

  const plugins = [
    new webpack.DefinePlugin({
      // Could use runtimeValue to update for changes on watch, but no real need
      BUILD_INFO: JSON.stringify({
        commit: describeCurrentCommit(),
        mode,
        timestamp: new Date().toISOString(),
      }),
    }),
    ...(env.reports ? [
      new BundleAnalyzerPlugin(),
    ] : []),
    ...(watch ? [
      new webpack.EvalSourceMapDevToolPlugin({}),
      new PreactRefreshPlugin(),
    ] : [
      new webpack.BannerPlugin({
        banner: 'Visual Meaning uses free software, see LICENSE.md for details.',
      }),
      ...(omitSourceMaps ? [] : [new webpack.SourceMapDevToolPlugin({
        filename: '[file].map',
      })]),
      new LicenseCheckerWebpackPlugin({
        allow: '(Apache-2.0 OR BSD-2-Clause OR BSD-3-Clause OR ISC OR MIT OR 0BSD OR MPL-2.0)',
        filter: /(^.*[/\\]node_modules[/\\]((?:@[^/\\]+[/\\])?(?:[^@/\\][^/\\]*)))/,
        // Loaders are imported in build but do not get used in final bundle
        ignore: ['babel-loader', 'source-map-loader'],
        outputFilename: 'js/LICENSE.md',
        outputWriter,
      }),
    ]),
  ];

  const optimization = isDev ? undefined : {
    minimizer: [
      new TerserWebpackPlugin({
        extractComments: false,
        terserOptions: {
          // Keep the addition of the banner mentioning external LICENSE.md only
          format: {comments: /^! Visual Meaning/},
          // Mangle openlayers private members convention with underscore suffix
          mangle: {properties: {regex: /_$/}},
        },
      }),
    ],
  };

  const watchConfig = env.WEBPACK_SERVE ? {} : {
    watch,
    watchOptions: {
      ignored: ['**/node_modules', '**/lang'],
      poll: isWindows ? 1000 : false,
    },
  };

  return {
    devServer: {
      host,
      hot: true,
      port,
      proxy: [
        {
          changeOrigin: true,
          context: ['/**'],
          headers: {
            Origin: new URL(apiUrl).origin,
            Referer: apiUrl,
          },
          target: apiUrl,
        },
      ],
      static: {
        directory: './dist',
      },
    },
    devtool: false,
    entry,
    mode,
    module: {
      rules: [{
        /* See https://webpack.js.org/guides/asset-management/ */
        generator: {
          filename: 'css/[name][ext]',
        },
        test: /\.css$/,
        type: 'asset/resource',
      }, {
        /* See https://webpack.js.org/loaders/source-map-loader/ */
        enforce: 'pre',
        include: /node_modules/,
        test: /\.js$/,
        use: {
          loader: 'source-map-loader',
        },
      }, {
        include: /node_modules[\\/](?:markdown-it-marked|ol|rbush)[\\/]/,
        test: /\.js$/,
        use: {
          loader: 'babel-loader',
        },
      }, {
        /* See https://webpack.js.org/loaders/babel-loader/ */
        exclude: /node_modules/,
        test: /\.(jsx?|tsx?)$/,
        use: {
          loader: 'babel-loader',
          options: {
            envName: mode + (watch ? '' : '-nowatch'),
          },
        },
      }],
    },
    optimization,
    output: {
      clean: {
        keep: /^lang/,
      },
      filename: 'js/[name].js',
      path: __dirname + '/dist',
      publicPath: '/static/',
    },
    plugins,
    resolve: {
      alias: {
        '@formatjs/icu-messageformat-parser': isDev
          ? '@formatjs/icu-messageformat-parser'
          : '@formatjs/icu-messageformat-parser/no-parser',
        'react': 'preact/compat',
        'react-dom': 'preact/compat',
        'style': __dirname + '/style',
      },
      extensions: ['.js', '.ts', '.tsx'],
    },
    ...watchConfig,
  };
};
