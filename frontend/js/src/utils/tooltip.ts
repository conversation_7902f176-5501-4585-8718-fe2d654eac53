/**
 * Utility functions for tooltip positioning and calculations
 */

export type TooltipPosition = 'top' | 'bottom' | 'left' | 'right';

export type VisualElementCenter = {
  centerX: number;
  centerY: number;
  rect: {
    left: number;
    right: number;
    top: number;
    bottom: number;
    width: number;
    height: number;
  } | null;
};

/**
 * Calculates the visual center of the target element excluding CSS margins.
 * This ensures tooltips are positioned relative to the actual visible element content.
 */
export function getVisualElementCenter(wrapperElement: HTMLElement): VisualElementCenter {
  if (!wrapperElement) {
    return {centerX: 0, centerY: 0, rect: null};
  }

  // Get the first child element (the actual target element)
  const childElement = wrapperElement.firstElementChild as HTMLElement;
  const targetElement = childElement || wrapperElement;

  const rect = targetElement.getBoundingClientRect();

  // getBoundingClientRect() gives us the border box which already excludes margins
  // This is the visual bounds of the actual target element
  const visualLeft = rect.left;
  const visualRight = rect.right;
  const visualTop = rect.top;
  const visualBottom = rect.bottom;

  const centerX = visualLeft + (visualRight - visualLeft) / 2;
  const centerY = visualTop + (visualBottom - visualTop) / 2;

  return {
    centerX,
    centerY,
    rect: {
      left: visualLeft,
      right: visualRight,
      top: visualTop,
      bottom: visualBottom,
      width: visualRight - visualLeft,
      height: visualBottom - visualTop,
    },
  };
}

/**
 * Calculates the optimal tooltip position based on available screen space
 * and the position of the target element.
 */
export function calculateOptimalPosition(
  wrapperElement: HTMLElement,
  preferredPosition: 'auto' | TooltipPosition,
): TooltipPosition {
  if (!wrapperElement || preferredPosition !== 'auto') {
    return preferredPosition === 'auto' ? 'top' : preferredPosition;
  }

  const rect = wrapperElement.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  // Approximate tooltip dimensions
  const tooltipWidth = 200; // Will be adjusted based on content
  const tooltipHeight = 60;
  const margin = 16;

  // Calculate available space in each direction
  const spaceTop = rect.top;
  const spaceBottom = viewportHeight - rect.bottom;
  const spaceLeft = rect.left;

  // Determine position based on screen location and available space
  // Top-left quadrant: prefer bottom
  if (rect.left < viewportWidth / 2 && rect.top < viewportHeight / 2) {
    return spaceBottom >= tooltipHeight + margin ? 'bottom' : 'top';
  }

  // Top-right quadrant: prefer bottom
  if (rect.left >= viewportWidth / 2 && rect.top < viewportHeight / 2) {
    return spaceBottom >= tooltipHeight + margin ? 'bottom' : 'top';
  }

  // Bottom-left quadrant: prefer top
  if (rect.left < viewportWidth / 2 && rect.top >= viewportHeight / 2) {
    return spaceTop >= tooltipHeight + margin ? 'top' : 'bottom';
  }

  // Bottom-right quadrant: prefer left
  if (rect.left >= viewportWidth / 2 && rect.top >= viewportHeight / 2) {
    return spaceLeft >= tooltipWidth + margin ? 'left' : 'right';
  }

  return 'top'; // fallback
}

/**
 * Checks if a child element is disabled in various ways
 */
export function isChildElementDisabled(wrapperElement: HTMLElement, disabled: boolean): boolean {
  if (!wrapperElement) {
    return false;
  }

  const childElement = wrapperElement.firstElementChild as HTMLElement;
  if (!childElement) {
    return false;
  }

  // Check various ways an element can be disabled
  return (
    childElement.hasAttribute('disabled')
    || childElement.getAttribute('aria-disabled') === 'true'
    || childElement.classList.contains('disabled')
    || disabled
  );
}

/**
 * Estimates tooltip width based on message length
 */
export function estimateTooltipWidth(message: string): number {
  return Math.min(Math.max(message.length * 8 + 32, 120), 300);
}

/**
 * Calculates tooltip positioning for top/bottom positions with edge detection
 */
export function calculateHorizontalPosition(
  visualCenter: VisualElementCenter,
  tooltipMessage: string,
  viewportWidth: number,
  margin: number = 8,
): {
    left: string | number;
    transform: string;
    maxWidth: number;
  } {
  const estimatedWidth = estimateTooltipWidth(tooltipMessage);
  const halfTooltipWidth = estimatedWidth / 2;
  const elementCenterX = visualCenter.centerX;

  // Unified margin for both left and right edge detection
  const wouldOverflowLeft = elementCenterX - halfTooltipWidth < margin;
  const wouldOverflowRight = elementCenterX + halfTooltipWidth > viewportWidth - margin;

  let tooltipLeft: string | number;
  if (wouldOverflowLeft) {
    tooltipLeft = margin;
  } else if (wouldOverflowRight) {
    // Move tooltip as close as possible to the target, but not past the right edge
    const maxLeft = viewportWidth - estimatedWidth - margin;
    tooltipLeft = Math.max(elementCenterX - halfTooltipWidth, maxLeft);
  } else {
    tooltipLeft = elementCenterX;
  }

  return {
    left: tooltipLeft,
    transform: typeof tooltipLeft === 'number' && !wouldOverflowLeft && !wouldOverflowRight ? 'translateX(-50%)' : 'none',
    maxWidth: viewportWidth - 2 * margin,
  };
}

/**
 * Calculates arrow position for top/bottom tooltips with edge detection
 */
export function calculateArrowPosition(
  visualCenter: VisualElementCenter,
  tooltipElement: HTMLElement,
  arrowSize: number = 6,
): {
    left: string;
    transform?: string;
  } {
  const tooltipRect = tooltipElement.getBoundingClientRect();
  const elementCenterX = visualCenter.centerX;
  const tooltipLeftX = tooltipRect.left;
  const tooltipWidth = tooltipRect.width;

  // Check if tooltip is normally centered (not repositioned due to edge detection)
  const tooltipCenterX = tooltipLeftX + tooltipWidth / 2;
  const isNormallyCentered = Math.abs(elementCenterX - tooltipCenterX) < 10;

  if (isNormallyCentered) {
    // Tooltip is centered - use simple centered arrow
    return {
      left: '50%',
      transform: 'translateX(-50%)',
    };
  }

  // Tooltip is repositioned due to edge detection - calculate precise arrow position
  const actualTooltipLeft = tooltipLeftX;
  const arrowLeftOffsetCorrected = elementCenterX - actualTooltipLeft;

  // Clamp arrow within tooltip bounds with minimal padding
  const minOffset = arrowSize; // allow arrow to reach the very edge
  const maxOffset = tooltipWidth - arrowSize;
  const clampedOffset = Math.max(minOffset, Math.min(maxOffset, arrowLeftOffsetCorrected));

  return {
    left: `${clampedOffset}px`,
  };
}
