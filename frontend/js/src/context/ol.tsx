// Provider for openlayers map
/** @jsx h */

import {Fragment, createContext, h, render} from 'preact';
import {render as renderToString} from 'preact-render-to-string';
import {useCallback, useEffect, useLayoutEffect, useMemo, useReducer, useRef} from 'preact/hooks';

import Map from 'ol/Map';
import OlView from 'ol/View';
import {Attribution, Zoom} from 'ol/control';
import {DragPan, KeyboardPan, KeyboardZoom, MouseWheelZoom, PinchZoom} from 'ol/interaction';
import Projection from 'ol/proj/Projection';

import addGestureCompat from '../functions/gestureCompat';
import {classes} from '../functions/html';
import useEnsuredContext from '../hooks/useEnsuredContext';
import {useTranslation} from '../intl';
import SVGMinus from '../svg/SVGMinus';
import SVGPlus from '../svg/SVGPlus';
import TooltipWrapper from '../components/shared/TooltipWrapper';
import {usePlatformStatesUpdater} from './platform';
import {useModel} from './triples';

import type {FrameState} from 'ol/Map';
import type {Extent} from 'ol/extent';
import type {Attrs, ComponentChildren, RNode} from '../types';

export type Coord = [number, number];

export type BackgroundType = string | (string & object);

export type ViewportTarget = {
  extent: Extent;
  scale: number;
};

type ProviderProps = {
  children: ComponentChildren;
  options: {
    extent: Extent;
    maxZoom?: number;
    minZoom?: number;
    worldExtent?: Extent;
    zoom?: number;
  };
};

type ContainerProps = Readonly<Omit<Attrs['div'], 'className'>> & {
  className?: string;
};

type Actions = (
  {type: 'ready'} |
  {type: 'unready'} |
  {type: 'animating'; y: boolean; target: ViewportTarget|undefined}
);

type Dispatch = (action: Actions) => void;

type State = {
  isAnimating?: true;
  ready: boolean;
  target?: ViewportTarget;
};

export const MAP_SIZE = 256;

const MapContext = createContext<{map: Map; state: State} | null>(null);
const MapActContext = createContext<Dispatch | null>(null);

export function translateCoord(coord: Coord): Coord {
  return [coord[1] - MAP_SIZE, MAP_SIZE + coord[0]];
}

export function translateExtent(extent: Extent): Extent {
  return [extent[1] - MAP_SIZE, extent[0] + MAP_SIZE, extent[3] - MAP_SIZE, extent[2] + MAP_SIZE];
}

const CENTER_COORD: Coord = translateCoord([0, 0]);

export function layerTransform(frameState: FrameState, scaleFactor = 1): {x: number; y: number; scale: number} {
  const scale = 1 / frameState.viewState.resolution / scaleFactor;
  const x = frameState.coordinateToPixelTransform[0] * CENTER_COORD[0]
    + frameState.coordinateToPixelTransform[2] * CENTER_COORD[1]
    + frameState.coordinateToPixelTransform[4];
  const y = frameState.coordinateToPixelTransform[1] * CENTER_COORD[0]
    + frameState.coordinateToPixelTransform[3] * CENTER_COORD[1]
    + frameState.coordinateToPixelTransform[5];

  return {x, y, scale};
}

const interactions = [
  // TODO: Make drag kinetic
  new DragPan(),
  new PinchZoom(),
  new MouseWheelZoom(),
  new KeyboardPan(),
  new KeyboardZoom(),
];

const controlsZoomDelta = 0.5;
const controls = [
  new Zoom({delta: controlsZoomDelta}),
  new Attribution({collapsible: false}),
];

function reduceMapState(state: State, action: Actions): State {
  switch (action.type) {
    case 'animating':
      if (action.y) {
        return {ready: true, isAnimating: true, target: action.target};
      }
      // fallthrough
    case 'ready':
      return {ready: true};
    case 'unready':
      // fallthrough
  }
  return {ready: false};
}

export function MapProvider({children, options}: ProviderProps): RNode {
  const intl = useTranslation();
  const mm = useModel();
  const updateStates = usePlatformStatesUpdater();
  const tileloadendHandler = useRef<(() => void)|null>(null);

  const map = useMemo(() => {
    return new Map({
      controls,
      interactions,
      view: new OlView({
        enableRotation: false,
        projection: new Projection({
          code: 'simple',
          axisOrientation: 'wnu',
          units: 'pixels',
          extent: options.extent,
          worldExtent: options.worldExtent,
        }),
        extent: options.worldExtent,
        minZoom: options.minZoom,
        maxZoom: options.maxZoom,
        showFullExtent: true,
        zoom: options.zoom,
      }),
    });
  }, [options]);

  useMemo(() => {
    tileloadendHandler.current = () => {
      updateStates({map: true});
    };
    map.on('loadend', tileloadendHandler.current);
  }, [map, updateStates]);

  useEffect(() => () => {
    if (tileloadendHandler.current) {
      map.un('loadend', tileloadendHandler.current);
      tileloadendHandler.current = null;
      updateStates({map: true});
    }
  }, [map, updateStates]);

  useLayoutEffect(() => {
    const controls = map.getControls();

    const zoomTemplate = document.createElement('template');
    zoomTemplate.innerHTML = renderToString(<Fragment>
      <SVGPlus id="vm-zoom-in-icon" />
      <SVGMinus id="vm-zoom-out-icon" />
    </Fragment>);

    const options = {
      delta: controlsZoomDelta,
      className: 'ol-zoom custom vm-float-block',
      zoomInClassName: 'ol-zoom-in',
      zoomOutClassName: 'ol-zoom-out',
      zoomInLabel: zoomTemplate.content.getElementById('vm-zoom-in-icon') || undefined,
      zoomOutLabel: zoomTemplate.content.getElementById('vm-zoom-out-icon') || undefined,
      zoomInTipLabel: intl.translate({defaultMessage: 'Zoom in'}),
      zoomOutTipLabel: intl.translate({defaultMessage: 'Zoom out'}),
      target: document.querySelector('#root div.ol-zoom-controls-container') as HTMLElement || undefined,
    };

    controls.setAt(0, new Zoom(options));

    // Add TooltipWrapper to zoom buttons after they're rendered
    const addTooltipsToZoomButtons = () => {
      const zoomInButton = document.querySelector<HTMLButtonElement>('.ol-zoom-in');
      const zoomOutButton = document.querySelector<HTMLButtonElement>('.ol-zoom-out');

      if (zoomInButton && !zoomInButton.hasAttribute('data-tooltip-wrapped')) {
        // Mark as wrapped to prevent duplicate wrapping
        zoomInButton.setAttribute('data-tooltip-wrapped', 'true');

        // Create wrapper container
        const wrapper = document.createElement('div');
        zoomInButton.parentNode?.insertBefore(wrapper, zoomInButton);

        // Store original properties
        const buttonClass = zoomInButton.className;
        const buttonHTML = zoomInButton.innerHTML;
        const isDisabled = zoomInButton.disabled;

        // Remove original button
        zoomInButton.remove();

        // Render TooltipWrapper with new button
        render(
          <TooltipWrapper tooltipMessage={intl.translate({defaultMessage: 'Zoom in'})}>
            <button
              className={buttonClass}
              onClick={() => {
                const view = map.getView();
                const currentZoom = view.getZoom() || 0;
                view.animate({zoom: currentZoom + controlsZoomDelta, duration: 250});
              }}
              disabled={isDisabled}
              dangerouslySetInnerHTML={{__html: buttonHTML}}
            />
          </TooltipWrapper>,
          wrapper,
        );
      }

      if (zoomOutButton && !zoomOutButton.hasAttribute('data-tooltip-wrapped')) {
        // Mark as wrapped to prevent duplicate wrapping
        zoomOutButton.setAttribute('data-tooltip-wrapped', 'true');

        // Create wrapper container
        const wrapper = document.createElement('div');
        zoomOutButton.parentNode?.insertBefore(wrapper, zoomOutButton);

        // Store original properties
        const buttonClass = zoomOutButton.className;
        const buttonHTML = zoomOutButton.innerHTML;
        const isDisabled = zoomOutButton.disabled;

        // Remove original button
        zoomOutButton.remove();

        // Render TooltipWrapper with new button
        render(
          <TooltipWrapper tooltipMessage={intl.translate({defaultMessage: 'Zoom out'})}>
            <button
              className={buttonClass}
              onClick={() => {
                const view = map.getView();
                const currentZoom = view.getZoom() || 0;
                view.animate({zoom: currentZoom - controlsZoomDelta, duration: 250});
              }}
              disabled={isDisabled}
              dangerouslySetInnerHTML={{__html: buttonHTML}}
            />
          </TooltipWrapper>,
          wrapper,
        );
      }
    };

    // Add tooltips after a small delay to ensure buttons are rendered
    const tooltipTimer = setTimeout(addTooltipsToZoomButtons, 100);

    return () => {
      clearTimeout(tooltipTimer);
      zoomTemplate.remove();
    };
  }, [intl, map, mm]);

  const [state, dispatch] = useReducer(reduceMapState, {ready: false});

  useEffect(() => {
    if (state.isAnimating) {
      updateStates({map: true});
    }
  }, [updateStates, state.isAnimating]);

  useEffect(() => {
    dispatch({type: 'ready'});
    return () => {
      dispatch({type: 'unready'});
    };

  }, [map, dispatch]);

  return <MapContext.Provider value={{map, state}}>
    <MapActContext.Provider value={dispatch}>
      {children}
    </MapActContext.Provider>
  </MapContext.Provider>;
}

export function MapContainer(props: ContainerProps): RNode {
  const {className, ...restProps} = props;
  const {map, state: {isAnimating}} = useMap();
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!window.ResizeObserver) {
      return undefined;
    }

    const {current} = mapRef;
    const observer = new ResizeObserver(() => {
      map.updateSize();
    });

    if (current) {
      observer.observe(current);
    }

    return () => {
      observer.disconnect();
    };
  }, [map]);

  useLayoutEffect(() => {
    ((window as unknown) as {vmMap: Map}).vmMap = map;
    const {current} = mapRef;
    if (current == null) {
      return undefined;
    }
    map.setTarget(current);
    map.getViewport().setAttribute('aria-hidden', 'true');
    const removeGestureCompat = addGestureCompat(current);
    return () => {
      // TODO: Upstream has this typed as HTMLElement|string|undefined but errors?
      map.setTarget((undefined as unknown) as string);
      removeGestureCompat();
    };
  }, [map]);

  return <div
    ref={mapRef}
    {...classes({'animating': isAnimating}, className)}
    {...restProps}
    tabIndex={0}
    aria-label="Map Viewer"
  />;
}

export function useMap(): Readonly<{map: Map; state: Readonly<State>}> {
  return useEnsuredContext(MapContext);
}

export function useMapStateChange(): {currentlyAnimating: (y: boolean, target?: ViewportTarget) => void} {
  const dispatch = useEnsuredContext(MapActContext);
  return useMemo(() => ({
    currentlyAnimating: (y, target) => dispatch({type: 'animating', y, target}),
  }), [dispatch]);
}

function reduceZoom(zoom: number): string {
  if (zoom < 3.0) {
    return 'vm-far';
  }
  if (zoom < 4.0) {
    return 'vm-mid';
  }
  return 'vm-near';
}

export function useZoom(): void {
  const {map} = useMap();
  const classRef = useRef<string>();

  const view = map.getView();
  const viewport = map.getViewport();

  const zoomInButton = document.querySelector<HTMLButtonElement>('.ol-zoom-in');
  const zoomOutButton = document.querySelector<HTMLButtonElement>('.ol-zoom-out');

  useEffect(() => {
    const onRes = () => {
      const currentZoom = parseFloat((view.getZoom() || 0).toFixed(2));
      const minZoom = parseFloat((view.getConstrainedZoom(view.getMinZoom()) || view.getMinZoom()).toFixed(2));
      const maxZoom = view.getMaxZoom();
      const className = reduceZoom(view.getZoom() || 0);
      if (viewport && className !== classRef.current) {
        classRef.current = className;
        viewport.className = 'ol-viewport ' + className;
      }

      if (zoomOutButton) {
        zoomOutButton.disabled = currentZoom <= minZoom ? true : false;
      }
      if (zoomInButton) {
        zoomInButton.disabled = currentZoom >= maxZoom ? true : false;
      }
    };
    view.on('change:resolution', onRes);
    onRes();
    return () => {
      view.un('change:resolution', onRes);
    };
  }, [view, viewport, zoomInButton, zoomOutButton]);
}

export function useMapCursorPosition(): () => Coord {
  const {map} = useMap();
  const pixelRef = useRef([0, 0]);

  useEffect(() => {
    const elem = map.getViewport();
    const onMove = (event: UIEvent): void => {
      pixelRef.current = map.getEventPixel(event);
    };
    elem.addEventListener('mousemove', onMove);
    return () => {
      elem.removeEventListener('mousemove', onMove);
    };
  }, [map]);

  return useCallback(() => {
    return translateCoord(map.getCoordinateFromPixelInternal(pixelRef.current) as Coord);
  }, [map]);
}

export function View(): null {
  return null;
}

export function TileLayer(): null {
  return null;
}

export function LayerGroup(props: {children: ComponentChildren}): RNode {
  return <Fragment>{props.children}</Fragment>;
}
