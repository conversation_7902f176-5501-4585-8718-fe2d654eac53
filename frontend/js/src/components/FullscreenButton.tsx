/** @jsx h */
import {h} from 'preact';
import {useEffect, useState} from 'preact/hooks';

import {useListContent} from '../context/page';
import {useScreenProperties} from '../context/screen';
import {isMobileDevice} from '../functions/device';
import {useTranslation} from '../intl';
import SVGCollapse from '../svg/SVGCollapse';
import SVGExpand from '../svg/SVGExpand';
import Button from './Button';

import type {RNode} from '../types';

export default function FullscreenButton(): RNode | null {
  const [isDomFullscreen, setIsDomFullscreen] = useState(false); // for fullscreen toggle clicking
  const [isBrowserFullscreen, setIsBrowserFullscreen] = useState(false); // for f11 fullscreen mode
  const {isLargeScreen} = useScreenProperties();
  const {open: isListMode} = useListContent();
  const intl = useTranslation();

  //handles toggle button click
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsDomFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  //handles f11 fullscreen mode
  useEffect(() => {
    const checkBrowserFullscreen = () => {
      const isFullscreen = window.innerHeight === screen.height;
      setIsBrowserFullscreen(isFullscreen);
    };

    checkBrowserFullscreen();
    window.addEventListener('resize', checkBrowserFullscreen);
    return () => {
      window.removeEventListener('resize', checkBrowserFullscreen);
    };
  }, []);

  if (isMobileDevice()) {
    return null;
  }

  const toggleFullscreen = async() => {
    if (!document.fullscreenElement) {
      try {
        await document.documentElement.requestFullscreen();
      } catch (error) {
        console.error('Failed to enter fullscreen mode:', error);
      }
    } else {
      try {
        await document.exitFullscreen();
      } catch (error) {
        console.error('Failed to exit fullscreen mode:', error);
      }
    }
  };

  const isFullscreen = isDomFullscreen || isBrowserFullscreen;
  const toggleDisabled = isBrowserFullscreen && !isDomFullscreen;

  return (
    <Button
      data-testid={isFullscreen ? 'exit-fullscreen-button' : 'fullscreen-button'}
      tooltipMessage={intl.translate({defaultMessage: 'Full Screen Mode'})}
      tooltipPosition="left"
      id="fullscreen-button"
      className={`vm-fullscreen-button ol-zoom-in${isListMode || !isLargeScreen ? ' vm-float-block' : ''}`}
      onClick={toggleFullscreen}
      disabled={toggleDisabled}
      title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}>
      {isFullscreen ? <SVGCollapse /> : <SVGExpand />}
    </Button>
  );
}
