// The Interaction Panel containing maps content table, description
// and addons
//
/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useCallback} from 'preact/hooks';

import {useControlPanelUpdater} from '../context/controlpanel';
import {useGeekery} from '../context/geek';
import {Panel, usePanelUpdaters} from '../context/panels';
import {usePlace} from '../context/place';
import {useScreenProperties} from '../context/screen';
import {useModel} from '../context/triples';
import {useTranslation} from '../intl';
import SVGBurger from '../svg/SVGBurger';
import SVGHome from '../svg/SVGHome';
import SVGInfo from '../svg/SVGInfo';
import SVGMapTerms from '../svg/SVGMapTerms';

import Button from './Button';
import MenuPanel from './MenuPanel';
import TooltipWrapper from './shared/TooltipWrapper';
import StoryPanel from './StoryPanel';

import type {ForwardRef} from '../types';
import type {PageProps} from './PageFloating';

export type Props = PageProps & {
  expandBurger: boolean;
  isListMode: boolean;
  onToggleBurger: (value?: boolean) => void;
};

const InteractionPanelFloating = forwardRef((props: Props, ref: ForwardRef<HTMLDivElement>) => {
  const {expandBurger, isListMode, onToggleBurger} = props;
  const intl = useTranslation();
  const mm = useModel();
  const {view} = usePlace();
  const {isLargeScreen} = useScreenProperties();
  const {setControl} = useControlPanelUpdater();
  const {changeOpen: changeOpenAbout} = usePanelUpdaters(Panel.About);
  const {showMapTermsButton} = useGeekery();

  const viewOrStory = view && mm.ofStoryOf(view) || view;
  const title: string = viewOrStory ? mm.nameOfOrFallback(viewOrStory) : props.title;

  const showStory = !expandBurger && !isListMode;

  const closeBurger = useCallback(() => onToggleBurger(false), [onToggleBurger]);

  const toggleBurger = () => {
    // On mobile, close search results or filter panel when burger is opened
    if (!isLargeScreen && !expandBurger) {
      setControl('search', false);
      setControl('filter', false);
    }
    onToggleBurger();
  };

  const currentUrl = window.location.href;
  const mapTermsUrl = currentUrl.replace(/\/maps\/([^/]+)\/.*/, '/mapterms/$1/');

  return <div
    className="vm-interaction vm-float-block"
    ref={ref}>
    <div className="vm-menu-header">
      <div className="vm-float-branding">
        <Button
          id="main-menu-button"
          tooltipMessage={intl.translate({defaultMessage: 'Main Menu'})}
          className="vm-interaction-button vm-toggle-button"
          aria-pressed={expandBurger}
          aria-expanded={expandBurger}
          onClick={toggleBurger}>
          <SVGBurger />
        </Button>
        <h1 className="vm-line-clamp" title={title}>{title}</h1>
      </div>
      {showMapTermsButton
      && <TooltipWrapper tooltipMessage={intl.translate({defaultMessage: 'Map terms'})}>
        <a href={mapTermsUrl} className="vm-interaction-button vm-toggle-button" title={intl.translate({defaultMessage: 'Map terms'})}>
          <SVGMapTerms />
        </a></TooltipWrapper>}
      <TooltipWrapper tooltipMessage={intl.translate({defaultMessage: 'Home'})}>
        <a id="home-button" href="/maps" className="vm-interaction-button vm-toggle-button" title={intl.translate({defaultMessage: 'Home'})}>
          <SVGHome />
        </a>
      </TooltipWrapper>
      <Button
        id="about-button"
        className="vm-interaction-button vm-toggle-button"
        tooltipMessage={intl.translate({defaultMessage: 'About tst'})}
        aria-pressed={false}
        onClick={() => changeOpenAbout(true)}>
        <SVGInfo />
      </Button>
    </div>
    {expandBurger && <MenuPanel
      {...props}
      onMenuItemClick={closeBurger} />}
    {showStory && <StoryPanel />}
  </div>;
});

export default InteractionPanelFloating;
