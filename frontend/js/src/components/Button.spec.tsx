/** @jsx h */
import {fireEvent, render, waitFor} from '@testing-library/preact';
import {h} from 'preact';

import Button from './Button';

// Mock the device detection function
jest.mock('../functions/device', () => ({
  isMobileDevice: jest.fn(() => false),
}));

describe('Button', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Create tooltip container for portal rendering
    const tooltipContainer = document.createElement('div');
    tooltipContainer.id = 'tooltip-container';
    document.body.appendChild(tooltipContainer);
  });

  afterEach(() => {
    // Clean up tooltip container
    const tooltipContainer = document.getElementById('tooltip-container');
    if (tooltipContainer) {
      document.body.removeChild(tooltipContainer);
    }
  });

  it('renders a basic button without tooltip', () => {
    const {container} = render(
      <Button>Click me</Button>,
    );

    const button = container.querySelector('button');
    expect(button).toBeTruthy();
    expect(button?.textContent).toBe('Click me');
    expect(button?.type).toBe('button');
  });

  it('passes through all standard button props', () => {
    const handleClick = jest.fn();
    const {container} = render(
      <Button
        id="test-button"
        className="test-class"
        disabled={true}
        onClick={handleClick}
        aria-label="Test button"
      >
        Test
      </Button>,
    );

    const button = container.querySelector('button');
    expect(button?.id).toBe('test-button');
    expect(button?.className).toBe('test-class');
    expect(button?.disabled).toBe(true);
    expect(button?.getAttribute('aria-label')).toBe('Test button');

    fireEvent.click(button!);
    expect(handleClick).toHaveBeenCalled();
  });

  it('renders button with tooltip when tooltipMessage is provided', async() => {
    const {container} = render(
      <Button tooltipMessage="Test tooltip">
        Button with tooltip
      </Button>,
    );

    const button = container.querySelector('button');
    expect(button).toBeTruthy();
    expect(button?.textContent).toBe('Button with tooltip');

    // Should be wrapped with tooltip functionality
    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear (200ms delay + 10ms animation delay)
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      expect(tooltipContainer?.textContent).toContain('Test tooltip');
    }, {timeout: 1000});
  });

  it('uses default tooltip position when not specified', async() => {
    const {container} = render(
      <Button tooltipMessage="Test tooltip">
        Test Button
      </Button>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]');
      expect(tooltip).toBeTruthy();
    }, {timeout: 1000});
  });

  it('respects custom tooltip position', async() => {
    const {container} = render(
      <Button tooltipMessage="Test tooltip" tooltipPosition="top">
        Test Button
      </Button>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]');
      expect(tooltip).toBeTruthy();
    }, {timeout: 1000});
  });

  it('does not render tooltip when tooltipMessage is empty', () => {
    const {container} = render(
      <Button tooltipMessage="">
        Test Button
      </Button>,
    );

    const button = container.querySelector('button');
    expect(button).toBeTruthy();

    // Should not be wrapped with tooltip
    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltip = container.querySelector('[role="tooltip"]');
    expect(tooltip).toBeFalsy();
  });

  it('works with forwardRef', () => {
    let buttonRef: HTMLButtonElement | null = null;

    render(
      <Button
        ref={(el: HTMLButtonElement | null) => {
          buttonRef = el;
        }}
        tooltipMessage="Test tooltip"
      >
        Ref test
      </Button>,
    );

    expect(buttonRef).toBeTruthy();
    expect(buttonRef!.textContent).toBe('Ref test');
  });

  it('works with refCallback prop', () => {
    let buttonRef: HTMLButtonElement | null = null;

    render(
      <Button
        refCallback={(el: HTMLButtonElement | null) => {
          buttonRef = el;
        }}
        tooltipMessage="Test tooltip"
      >
        RefCallback test
      </Button>,
    );

    expect(buttonRef).toBeTruthy();
    expect(buttonRef!.textContent).toBe('RefCallback test');
  });

  it('maintains backward compatibility with existing usage patterns', async() => {
    // Test the pattern used in existing code
    const {container} = render(
      <Button
        className="vm-toggle-button"
        aria-pressed={false}
        tooltipMessage="Comments"
        onClick={() => {}}
      >
        Button content
      </Button>,
    );

    const button = container.querySelector('button');
    expect(button?.className).toBe('vm-toggle-button');
    expect(button?.getAttribute('aria-pressed')).toBe('false');

    // Should have tooltip functionality
    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      expect(tooltipContainer?.textContent).toContain('Comments');
    }, {timeout: 1000});
  });

  it('handles disabled buttons correctly with tooltips', () => {
    const {container} = render(
      <Button disabled tooltipMessage="Disabled button tooltip">
        Disabled Button
      </Button>,
    );

    const button = container.querySelector('button');
    expect(button?.disabled).toBe(true);

    // Tooltip should still be present but hidden due to disabled state
    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // The tooltip wrapper should detect the disabled state and not show tooltip
    expect(container.textContent).not.toContain('Disabled button tooltip');
  });

  it('supports all tooltip positions', async() => {
    const positions: Array<'auto' | 'top' | 'bottom' | 'left' | 'right'> = ['auto', 'top', 'bottom', 'left', 'right'];

    for (const position of positions) {
      const {container} = render(
        <Button tooltipMessage="Test tooltip" tooltipPosition={position}>
          {position} Button
        </Button>,
      );

      const wrapper = container.firstChild as HTMLElement;
      fireEvent.mouseEnter(wrapper);

      // Wait for tooltip to appear
      await waitFor(() => {
        const tooltipContainer = document.getElementById('tooltip-container');
        const tooltip = tooltipContainer?.querySelector('[role="tooltip"]');
        expect(tooltip).toBeTruthy();
      }, {timeout: 1000});
    }
  });
});
