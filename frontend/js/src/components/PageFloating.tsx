// Layout of major components within in the page
//
// This is going to be an ongoing battle to keep tidy.
/** @jsx h */

import {Fragment, h} from 'preact';
import {forwardRef} from 'preact/compat';
import {useCallback, useEffect, useLayoutEffect, useRef, useState} from 'preact/hooks';

import {useControlPanelActiveControl, useSearchControl} from '../context/controlpanel';
import {useListContent} from '../context/page';
import {Panel, usePanel, usePanelUpdaters} from '../context/panels';
import {useMatchingView, useNavigation, usePlace} from '../context/place';
import {useScreenProperties} from '../context/screen';
import {useModel} from '../context/triples';
import flag from '../flags';
import useFilteredItems from '../hooks/useFilteredItems';
import useResizePanelMaxDimension from '../hooks/useResizePanelMaxDimension';

import {classes} from '../functions/html';
import CommentsPanelFloating from './CommentsPanelFloating';
import CommentsToggleFloating from './CommentsToggleFloating';
import ControlPanelFloating from './ControlPanelFloating';
import {MediaDialog} from './EnlargedMediaDialog';
import FilterTogglesFloating from './FilterTogglesFloating';
import FullscreenButton from './FullscreenButton';
import GeekLensDisplay from './GeekLensDisplay';
import GeekPanel from './GeekPanel';
import InteractionPanelFloating from './InteractionPanelFloating';
import IntroSplash from './IntroSplash';
import MapSelectorFloating, {MapSelectorFloatingOverlay} from './MapSelectorFloating';
import SelectedItemFloating from './SelectedItemFloating';
import VoterStart from './VoterStart';

import type {SupportedLocale} from '../context/intl';
import type {ForwardRef, RNode, RefObject} from '../types';
import type {LanguageProps} from './LanguageSwitch';


export type PageProps = LanguageProps & Readonly<{
  title: string;
  map: RNode;
  lang: string;
  setLang: (lang: SupportedLocale) => void;
}>;

const MANY = 30;

function useViewExtras() {
  const [isVoting, _v] = useMatchingView('voting');
  const [showVote, setShowVote] = useState(true);
  const {length} = useFilteredItems();
  const mm = useModel();

  return {
    hasItems: length > 0,
    manyItems: length >= MANY && !flag(mm, 'edgeDisplay'),
    votingSplash: isVoting ? <VoterStart show={showVote} setShow={setShowVote} /> : null,
  };
}


type ContextFloatingProps = {
  mapSwitchToggleRef?: RefObject<HTMLDivElement>;
  commentsOpen: boolean;
  expandMapsSwitcher: boolean;
  commentsEnabled: boolean;
  showContextButtons: boolean;
  toggleMapSwitcher: () => void;
};
const ContextFloating = forwardRef((props: ContextFloatingProps, ref: ForwardRef<HTMLDivElement>): RNode => {
  const {mapSwitchToggleRef,
    commentsOpen,
    commentsEnabled,
    showContextButtons, expandMapsSwitcher,
    toggleMapSwitcher} = props;
  const showComments = commentsOpen && commentsEnabled;
  const showButtons = showContextButtons && !showComments;
  return <div className="vm-float-container-side" ref={ref}>
    {showButtons && <div className="vm-button-group">
      <div className="vm-button-group">
        {commentsEnabled && <CommentsToggleFloating
          toggleEnabled={commentsOpen} />}
        <MapSelectorFloating
          ref={mapSwitchToggleRef}
          onToggle={toggleMapSwitcher}
          expand={expandMapsSwitcher} />
      </div>
    </div>}
    {showComments && <CommentsPanelFloating />}
  </div>;
});

export function FloatingComponents(props: PageProps): RNode {
  const {open: isListMode} = useListContent();
  const mm = useModel();
  const {manyItems, votingSplash} = useViewExtras();
  const {isLargeScreen} = useScreenProperties();
  const activeControl = useControlPanelActiveControl();
  const {setView} = useNavigation();
  const {hint, selection, view} = usePlace();
  const isStory = view != null && mm.ofStoryOf(view) != null;
  const [mobileKeyboardVisible, setMobileKeyboardVisible] = useState(false);

  const {open: commentsOpen} = usePanel(Panel.Comments);
  const {open: introOpen} = usePanel(Panel.About);
  const {toggleOpen: toggleOverlayMapSwitcher} = usePanelUpdaters(Panel.MapSelector);

  const [expandBurger, setExpandBurger] = useState(false);
  const [expandMapsSwitcher, setExpandMapsSwitcher] = useState(false);

  const contextContentRef = useRef<HTMLDivElement>(null);
  const mapSwitcherToggleRef = useRef<HTMLDivElement>(null);
  const interactionRef = useRef<HTMLDivElement>(null);
  const filterTogglesRef = useRef<HTMLDivElement>(null);
  const containerTopRef = useRef<HTMLDivElement>(null);
  const {search} = useSearchControl();

  const [isIntroView] = useMatchingView('introduction');

  useEffect(() => {
    document.body.classList.toggle('vm-many', manyItems);
  }, [manyItems]);

  useEffect(() => {
    if (search && document.activeElement === document.querySelector('input.vm-search-input')) {
      setMobileKeyboardVisible(true);
    } else {
      setMobileKeyboardVisible(false);
    }
  }, [search]);


  // Tracks the visibility of the mobile keyboard by listening to focus and blur events on the search input.
  useEffect(() => {
    const handleFocus = (event:Event) => {
      const target = event.target as HTMLInputElement;
      if (target.value) {
        setMobileKeyboardVisible(true);
      }
    };

    const handleBlur = () => {
      // Delay to ensure clicks complete before updating the keyboard state.
      setTimeout(() => {
        setMobileKeyboardVisible(false);
      }, 0);
    };

    const input = document.querySelector('input.vm-search-input');
    if (input) {
      input.addEventListener('focus', handleFocus);
      input.addEventListener('blur', handleBlur);
    }

    return () => {
      if (input) {
        input.removeEventListener('focus', handleFocus);
        input.removeEventListener('blur', handleBlur);
      }
    };
  }, []);

  // Close the mobile keyboard when a touch event occurs on the search results panel.
  useEffect(() => {
    // Blurs the search input and hides the mobile keyboard.
    const handleTouch = () => {
      const input = document.querySelector('input.vm-search-input') as HTMLElement | null;
      if (input) {
        input.blur();
      }
    };

    const searchResultsPanel = document.querySelector('.vm-search-results');

    if (mobileKeyboardVisible && searchResultsPanel) {
      searchResultsPanel.addEventListener('touchstart', handleTouch);
    }

    return () => {
      const searchResultsPanel = document.querySelector('.vm-search-results');
      if (searchResultsPanel) {
        searchResultsPanel.removeEventListener('touchstart', handleTouch);
      }
    };
  }, [mobileKeyboardVisible]);


  // On mobile, close burger when an item is selected
  useEffect(() => {
    if (!isLargeScreen
        && !!selection
        && (hint.name === 'map' || hint.name === 'itemList')) {
      setExpandBurger(false);
    }
  }, [isLargeScreen, hint, selection]);

  // On mobile, close burger when search results or filter panel is opened
  useEffect(() => {
    if (!isLargeScreen && activeControl === 'search' || activeControl === 'filter') {
      setExpandBurger(false);
    }
  }, [activeControl, isLargeScreen]);

  useEffect(() => {
    if (commentsOpen) {
      contextContentRef.current?.focus();
    }
  }, [commentsOpen]);

  const resetFromIntro = useCallback(() => {
    if (isIntroView) {
      setView(null);
    }
  }, [isIntroView, setView]);

  const toggleMapsSwitcherExpand = useCallback(() => {
    setExpandMapsSwitcher(em => !em);
    if (!isLargeScreen) {
      toggleOverlayMapSwitcher();
    }
  }, [toggleOverlayMapSwitcher, isLargeScreen]);

  const toggleBurgerExpand = useCallback((value?: boolean) => {
    setExpandBurger(value === undefined ? v => !v : value);
  }, []);

  const commentsEnabled = flag(mm, 'itemSidebar');
  const showSIP = isLargeScreen || !expandBurger;
  const showContextButtons = isLargeScreen || !expandBurger;
  const showInteraction = isLargeScreen || !commentsOpen;
  const showFilterToggles = isLargeScreen || !commentsOpen && !expandBurger;
  const mobileViewInteractionExpanded = !isLargeScreen && !isListMode && (expandBurger || isStory);
  const containerTopWrap = !isLargeScreen ? 'wrap' : 'nowrap';
  const containerTopJustify = mobileViewInteractionExpanded ? 'space-between' : 'flex-start';

  useResizePanelMaxDimension(containerTopRef, [contextContentRef], 'width', isLargeScreen);
  useResizePanelMaxDimension(filterTogglesRef, [contextContentRef], 'width', !isLargeScreen);

  const contextFloating = <ContextFloating
    ref={contextContentRef}
    mapSwitchToggleRef={mapSwitcherToggleRef}
    commentsOpen={commentsOpen}
    expandMapsSwitcher={expandMapsSwitcher}
    commentsEnabled={commentsEnabled}
    showContextButtons={showContextButtons}
    toggleMapSwitcher={toggleMapsSwitcherExpand} />;


  useLayoutEffect(() => {
    const indicationContainer = document.querySelector('.vm-layer.vm-content');

    if (containerTopRef.current?.querySelector('.vm-filter-toggle-container, .vm-float-map-select, .vm-comments-toggle')) {
      indicationContainer?.classList.toggle('has-secondary-control', true);
    }
  });

  return <Fragment>
    <div className="vm-layer vm-float" inert={introOpen}>
      <div className="vm-float-container base">
        <div {...classes({'keyboard-visible': mobileKeyboardVisible}, 'vm-float-container-bottom')}>
          <ControlPanelFloating
            {...props} />
          <SelectedItemFloating enabled={showSIP} />
        </div>
        <div
          className="vm-float-container-top"
          ref={containerTopRef}
          style={{flexWrap: containerTopWrap, justifyContent: containerTopJustify}}>
          {showInteraction && <InteractionPanelFloating
            ref={interactionRef}
            {...props}
            {...{expandBurger, isListMode, onToggleBurger: toggleBurgerExpand}} />}
          {showFilterToggles && <FilterTogglesFloating ref={filterTogglesRef} />}
          {!isLargeScreen && contextFloating}
        </div>
      </div>
      {isLargeScreen && <div className="vm-float-container context">
        {contextFloating}
      </div>}
      {!isLargeScreen && <div className="vm-float-container overlay">
        <MapSelectorFloatingOverlay
          mapSwitcherToggleRef={mapSwitcherToggleRef}
        />
      </div>}
    </div>
    <GeekPanel />
    <GeekLensDisplay />
    <MediaDialog />
    {(introOpen) && <IntroSplash onClose={resetFromIntro} />}
    {votingSplash}
    <div className="ol-zoom-controls-container"></div>
    <FullscreenButton />
  </Fragment>;
}
