// The Control panel containing most of the functionality for searching and filtering data
//
/** @jsx h */

import {h} from 'preact';
import {useCallback, useRef} from 'preact/hooks';

import {useControlPanelActiveControl, useControlPanelUpdater, useSearchControl} from '../context/controlpanel';
import {PANEL_LABELS, Panel, usePanelUpdaters} from '../context/panels';
import {useFiltersPartialReset} from '../context/place';
import {useDebounce} from '../hooks/useDebounce';
import {useTranslation} from '../intl';
import SVGFilter from '../svg/SVGFilter';
import SVGFilterRestore from '../svg/SVGFilterRestore';
import SVGMagnifyingGlass from '../svg/SVGMagnifyingGlass';
import Button from './Button';
import FilterControls from './FilterControls';
import {MapListToggle} from './MapListModeFloating';
import SearchRecent from './SearchRecent';
import SearchResults from './SearchResults';
import ClearInputButton from './shared/ClearInputButton';
import PanelHeader from './shared/PanelHeader';

import type {RNode} from '../types';

export type ComponentProps = Readonly<{
  title: string;
}>;
export default function ControlPanelFloating(props: ComponentProps): RNode {
  const {title} = props;
  const {search, setSearch} = useSearchControl();
  const intl = useTranslation();
  const activeControl = useControlPanelActiveControl();
  const {toggleControl, setControl} = useControlPanelUpdater();
  const {isResettable: areFiltersResettable, reset: resetFilters} = useFiltersPartialReset();
  const searchRef = useRef<HTMLInputElement>(null);
  const {toggleOpen: toggleFiltersOpen} = usePanelUpdaters(Panel.Filters);

  const searchContentRef = useRef<HTMLElement>(null);

  const expandFilter = activeControl === 'filter';
  const expandSearch = activeControl === 'search';

  const onSearchFocus = useCallback(() => {
    setControl('search', true);
  }, [setControl]);

  const {debouncedFunction: tempSearchChange, clearDebounce} = useDebounce((e: Event) => {
    const value = (e.target as HTMLInputElement|null)?.value || '';
    setSearch(value);
  }, [setSearch, setControl]);

  const onClearInput = () => {
    setSearch('');
    clearDebounce();
  };

  const searchKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === 'ArrowDown') {
      if (e.key === 'Enter') {
        const value = (e.target as HTMLInputElement | null)?.value || '';
        setSearch(value);
        clearDebounce();
      }
      e.stopPropagation();
      // Defer focus until after DOM updates
      setTimeout(() => {
        searchContentRef.current?.querySelector<HTMLElement>('.vm-search-item[tabindex="0"], .vm-label-button[tabindex="0"], p')?.focus();
      }, 0);
      e.preventDefault();
    }
  };

  const onSearchClose = useCallback(() => {
    setControl('search', false);
    clearDebounce();
    setSearch('');
  }, [clearDebounce, setControl, setSearch]);

  const onSearchItemSelected = useCallback(() => {
    setControl('search', false);
  }, [setControl]);

  const onFilterClose = useCallback(() => {
    setControl('filter', false);
  }, [setControl]);

  return <div className="vm-float-control-panel vm-float-block">
    <div className="vm-float-search-bar">
      <div className="vm-clearable-input-container">
        <input
          id="search-input"
          type="search"
          ref={searchRef}
          placeholder={intl.translate({
            defaultMessage: 'Search {map}',
          }, {map: title})}
          value={search}
          onKeyDown={searchKeyDown}
          onChange={tempSearchChange}
          onFocus={onSearchFocus}
          className="vm-search-input"
        />
        <SVGMagnifyingGlass className="vm-search-icon vm-svg" />
        <ClearInputButton value={search} onClearInput={onClearInput} />
      </div>
      <Button
        className="vm-filter-restore-button vm-toggle-button"
        id="reset-filters-button"
        tooltipMessage={intl.translate({defaultMessage: 'Restore default filters'})}
        onClick={() => resetFilters()}
        disabled={!areFiltersResettable}
      >
        <SVGFilterRestore />
      </Button>
      <Button
        className="vm-expand-filter-button vm-toggle-button"
        id="filter-panel-button"
        tooltipMessage={intl.translate({defaultMessage: 'Filters'})}
        aria-pressed={expandFilter}
        aria-expanded={expandFilter}
        onClick={() => {
          // TODO: sync both open states
          toggleControl('filter');
          toggleFiltersOpen();
        }}
      >
        <SVGFilter />
      </Button>
      <MapListToggle />
    </div>
    {expandSearch
      && (search
        && <SearchResults
          contentRef={searchContentRef}
          parentRef={searchRef}
          searchTerm={search}
          onItemSelected={onSearchItemSelected}
          onClose={onSearchClose}
        />
        || <SearchRecent
          contentRef={searchContentRef}
          parentRef={searchRef}
          onTermSelected={setSearch}
          onClose={onSearchClose}
        /> || null)}
    {expandFilter && <section className="vm-float-block-section" aria-label={PANEL_LABELS[Panel.Filters]}>
      <PanelHeader
        panel={Panel.Filters}
        icon={<SVGFilter />}
        title={intl.translate({defaultMessage: 'Filters'})}
        onClose={onFilterClose} />
      <FilterControls />
    </section>}
  </div>;
}
