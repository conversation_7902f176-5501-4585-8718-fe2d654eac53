// Basic Button component wrapper with built-in tooltip functionality.
/** @jsx h */

import {h} from 'preact';
import {forwardRef} from 'preact/compat';

import TooltipWrapper from './shared/TooltipWrapper';

import type {Attrs, ForwardRef, RNode, RefCallback} from '../types';

type ButtonProps = Attrs['button'] & {
  tooltipMessage?: string;
  tooltipPosition?: 'auto' | 'top' | 'bottom' | 'left' | 'right';
  refCallback?: RefCallback<HTMLButtonElement>;
};

/**
 * Button component with optional built-in tooltip functionality.
 *
 * @param tooltipMessage - Optional tooltip text to display on hover/focus
 * @param tooltipPosition - Optional tooltip positioning ('auto' by default)
 * @param refCallback - Optional ref callback for the button element
 * @param children - Button content
 * @param ...restProps - All other standard button props
 */
const Button = forwardRef((props: ButtonProps, forwardedRef: ForwardRef<HTMLButtonElement>): RNode => {
  const {children, tooltipMessage, tooltipPosition = 'auto', refCallback, ...restProps} = props;
  const ref = forwardedRef || refCallback;

  // If tooltipMessage is provided, wrap with TooltipWrapper
  if (tooltipMessage) {
    const buttonElement = (
      <button
        {...restProps}
        ref={ref}
        type="button"
        aria-label={tooltipMessage}
      >
        {children}
      </button>
    );

    return (
      <TooltipWrapper
        tooltipMessage={tooltipMessage}
        position={tooltipPosition}
      >
        {buttonElement}
      </TooltipWrapper>
    );
  }

  // Regular button without tooltip
  const buttonElement = (
    <button
      {...restProps}
      ref={ref}
      type="button"
    >
      {children}
    </button>
  );

  // Otherwise, return the button without tooltip wrapper
  return buttonElement;
});

export default Button;
