/** @jsx h */
import {fireEvent, render, waitFor} from '@testing-library/preact';
import {h} from 'preact';

import TooltipWrapper from './TooltipWrapper';
import * as deviceModule from '../../functions/device';

// Mock the device detection function
jest.mock('../../functions/device', () => ({
  isMobileDevice: jest.fn(() => false),
}));

describe('TooltipWrapper', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Create tooltip container for portal rendering
    const tooltipContainer = document.createElement('div');
    tooltipContainer.id = 'tooltip-container';
    document.body.appendChild(tooltipContainer);
  });

  afterEach(() => {
    // Clean up tooltip container
    const tooltipContainer = document.getElementById('tooltip-container');
    if (tooltipContainer) {
      document.body.removeChild(tooltipContainer);
    }
  });

  it('renders children correctly', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    expect(container.querySelector('button')).toBeTruthy();
    expect(container.textContent).toContain('Test Button');
  });

  it('shows tooltip on mouse enter', async() => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear (there's a 10ms delay + 200ms show delay)
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      expect(tooltipContainer?.textContent).toContain('Test tooltip');
    }, {timeout: 1000});
  });

  it('hides tooltip on mouse leave', async() => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      expect(tooltipContainer?.textContent).toContain('Test tooltip');
    }, {timeout: 1000});

    fireEvent.mouseLeave(wrapper);

    // Wait for tooltip to hide (should have hidden class)
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
      expect(tooltip).toBeTruthy();
      expect(tooltip.classList.contains('vm-tooltip--hidden')).toBe(true);
    }, {timeout: 1000});
  });

  it('shows tooltip on focus', async() => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.focus(wrapper);

    // Wait for tooltip to appear
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      expect(tooltipContainer?.textContent).toContain('Test tooltip');
    }, {timeout: 1000});
  });

  it('hides tooltip on blur', async() => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.focus(wrapper);

    // Wait for tooltip to appear
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      expect(tooltipContainer?.textContent).toContain('Test tooltip');
    }, {timeout: 1000});

    fireEvent.blur(wrapper);

    // Wait for tooltip to hide
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
      expect(tooltip).toBeTruthy();
      expect(tooltip.classList.contains('vm-tooltip--hidden')).toBe(true);
    }, {timeout: 1000});
  });

  it('does not show tooltip when disabled', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip" disabled={true}>
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).not.toContain('Test tooltip');
  });

  it('does not show tooltip on mobile devices', () => {
    // Mock mobile device detection
    const mockIsMobile = jest.spyOn(deviceModule, 'isMobileDevice').mockReturnValue(true);

    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).not.toContain('Test tooltip');

    // Reset mock
    mockIsMobile.mockRestore();
  });

  it('does not show tooltip for disabled child elements', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button disabled>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).not.toContain('Test tooltip');
  });

  it('does not show tooltip for aria-disabled child elements', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button aria-disabled="true">Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).not.toContain('Test tooltip');
  });

  it('applies correct ARIA attributes to tooltip', async() => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear and check ARIA attributes
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]');
      expect(tooltip).toBeTruthy();
      expect(tooltip?.getAttribute('role')).toBe('tooltip');
      expect(tooltip?.getAttribute('aria-hidden')).toBe('false');
    }, {timeout: 1000});
  });

  it('supports different positioning options', async() => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip" position="bottom">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]');
      expect(tooltip).toBeTruthy();
      expect(tooltipContainer?.textContent).toContain('Test tooltip');
    }, {timeout: 1000});
  });

  it('does not show tooltip when message is empty', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    const tooltip = tooltipContainer?.querySelector('[role="tooltip"]');
    expect(tooltip).toBeFalsy();
  });

  it('uses consolidated event handlers for showing tooltip', async() => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;

    // Test mouse enter
    fireEvent.mouseEnter(wrapper);
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      expect(tooltipContainer?.textContent).toContain('Test tooltip');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
      expect(tooltip.classList.contains('vm-tooltip--hidden')).toBe(false);
    }, {timeout: 1000});

    // Test mouse leave
    fireEvent.mouseLeave(wrapper);
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
      expect(tooltip.classList.contains('vm-tooltip--hidden')).toBe(true);
    }, {timeout: 1000});

    // Test focus - just verify tooltip appears
    fireEvent.focus(wrapper);
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      expect(tooltipContainer?.textContent).toContain('Test tooltip');
    }, {timeout: 1000});
  });

  it('handles edge detection for long tooltip messages', async() => {
    // Mock getBoundingClientRect to simulate element near viewport edge
    const mockGetBoundingClientRect = jest.fn(() => ({
      left: 10, // Near left edge
      top: 100,
      right: 50,
      bottom: 120,
      width: 40,
      height: 20,
      x: 10,
      y: 100,
      toJSON: () => ({}),
    })) as jest.MockedFunction<() => DOMRect>;

    // Mock window.innerWidth
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 400,
    });

    const {container} = render(
      <TooltipWrapper tooltipMessage="This is a very long tooltip message that should trigger edge detection">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    wrapper.getBoundingClientRect = mockGetBoundingClientRect;

    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
      expect(tooltip).toBeTruthy();
    }, {timeout: 1000});

    // Cleanup
    mockGetBoundingClientRect.mockRestore();
  });

  it('centers arrow correctly for normal tooltips vs stretched tooltips', async() => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Short tooltip" position="bottom">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    // Wait for tooltip to appear
    await waitFor(() => {
      const tooltipContainer = document.getElementById('tooltip-container');
      const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
      expect(tooltip).toBeTruthy();
      // For normal tooltips with enough space, arrow should be centered using CSS transforms
      // The exact implementation may vary, but we can verify the tooltip exists and is positioned
      expect(tooltip.classList.contains('vm-tooltip--hidden')).toBe(false);
    }, {timeout: 1000});
  });
});
