/** @jsx h */
import {fireEvent, render} from '@testing-library/preact';
import {h} from 'preact';

import TooltipWrapper from './TooltipWrapper';
import * as deviceModule from '../../functions/device';

// Mock the device detection function
jest.mock('../../functions/device', () => ({
  isMobileDevice: jest.fn(() => false),
}));

describe('TooltipWrapper', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Create tooltip container for portal rendering
    const tooltipContainer = document.createElement('div');
    tooltipContainer.id = 'tooltip-container';
    document.body.appendChild(tooltipContainer);
  });

  afterEach(() => {
    // Clean up tooltip container
    const tooltipContainer = document.getElementById('tooltip-container');
    if (tooltipContainer) {
      document.body.removeChild(tooltipContainer);
    }
  });

  it('renders children correctly', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    expect(container.querySelector('button')).toBeTruthy();
    expect(container.textContent).toContain('Test Button');
  });

  it('shows tooltip on mouse enter', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).toContain('Test tooltip');
  });

  it('hides tooltip on mouse leave', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);
    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).toContain('Test tooltip');

    fireEvent.mouseLeave(wrapper);
    // Tooltip should still be in DOM but with opacity 0
    const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
    expect(tooltip).toBeTruthy();
    expect(tooltip.style.opacity).toBe('0');
  });

  it('shows tooltip on focus', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.focus(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).toContain('Test tooltip');
  });

  it('hides tooltip on blur', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.focus(wrapper);
    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).toContain('Test tooltip');

    fireEvent.blur(wrapper);
    const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
    expect(tooltip).toBeTruthy();
    expect(tooltip.style.opacity).toBe('0');
  });

  it('does not show tooltip when disabled', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip" disabled={true}>
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).not.toContain('Test tooltip');
  });

  it('does not show tooltip on mobile devices', () => {
    // Mock mobile device detection
    const mockIsMobile = jest.spyOn(deviceModule, 'isMobileDevice').mockReturnValue(true);

    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).not.toContain('Test tooltip');

    // Reset mock
    mockIsMobile.mockRestore();
  });

  it('does not show tooltip for disabled child elements', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button disabled>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).not.toContain('Test tooltip');
  });

  it('does not show tooltip for aria-disabled child elements', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button aria-disabled="true">Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    expect(tooltipContainer?.textContent).not.toContain('Test tooltip');
  });

  it('applies correct ARIA attributes to tooltip', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const tooltipContainer = document.getElementById('tooltip-container');
    // Tooltip should be in DOM initially but hidden
    const tooltip = tooltipContainer?.querySelector('[role="tooltip"]');
    expect(tooltip).toBeTruthy();
    expect(tooltip?.getAttribute('role')).toBe('tooltip');
    expect(tooltip?.getAttribute('aria-hidden')).toBe('true');

    // Show tooltip and check aria-hidden changes
    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);
    expect(tooltip?.getAttribute('aria-hidden')).toBe('false');
  });

  it('supports different positioning options', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip" position="bottom">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const tooltipContainer = document.getElementById('tooltip-container');
    // Tooltip should be in DOM
    const tooltip = tooltipContainer?.querySelector('[role="tooltip"]');
    expect(tooltip).toBeTruthy();

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    expect(tooltipContainer?.textContent).toContain('Test tooltip');
  });

  it('does not show tooltip when message is empty', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    const tooltip = tooltipContainer?.querySelector('[role="tooltip"]');
    expect(tooltip).toBeFalsy();
  });

  it('uses consolidated event handlers for showing tooltip', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Test tooltip">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    const tooltipContainer = document.getElementById('tooltip-container');

    // Test mouse enter
    fireEvent.mouseEnter(wrapper);
    expect(tooltipContainer?.textContent).toContain('Test tooltip');
    let tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
    expect(tooltip.style.opacity).toBe('1');

    // Test mouse leave
    fireEvent.mouseLeave(wrapper);
    tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
    expect(tooltip.style.opacity).toBe('0');

    // Test focus - just verify tooltip appears
    fireEvent.focus(wrapper);
    expect(tooltipContainer?.textContent).toContain('Test tooltip');

    // Test blur
    fireEvent.blur(wrapper);
    tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
    expect(tooltip.style.opacity).toBe('0');
  });

  it('handles edge detection for long tooltip messages', () => {
    // Mock getBoundingClientRect to simulate element near viewport edge
    const mockGetBoundingClientRect = jest.fn(() => ({
      left: 10, // Near left edge
      top: 100,
      right: 50,
      bottom: 120,
      width: 40,
      height: 20,
      x: 10,
      y: 100,
      toJSON: () => ({}),
    })) as jest.MockedFunction<() => DOMRect>;

    // Mock window.innerWidth
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 400,
    });

    const {container} = render(
      <TooltipWrapper tooltipMessage="This is a very long tooltip message that should trigger edge detection">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    wrapper.getBoundingClientRect = mockGetBoundingClientRect;

    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
    expect(tooltip).toBeTruthy();

    // Cleanup
    mockGetBoundingClientRect.mockRestore();
  });

  it('centers arrow correctly for normal tooltips vs stretched tooltips', () => {
    const {container} = render(
      <TooltipWrapper tooltipMessage="Short tooltip" position="bottom">
        <button>Test Button</button>
      </TooltipWrapper>,
    );

    const wrapper = container.firstChild as HTMLElement;
    fireEvent.mouseEnter(wrapper);

    const tooltipContainer = document.getElementById('tooltip-container');
    const tooltip = tooltipContainer?.querySelector('[role="tooltip"]') as HTMLElement;
    expect(tooltip).toBeTruthy();

    // For normal tooltips with enough space, arrow should be centered using CSS transforms
    // The exact implementation may vary, but we can verify the tooltip exists and is positioned
    expect(tooltip.style.opacity).toBe('1');
  });
});
