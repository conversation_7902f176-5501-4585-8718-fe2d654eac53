<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tooltip Portal Test</title>
    <style>
        .container {
            width: 300px;
            height: 200px;
            border: 2px solid #ccc;
            overflow: hidden;
            position: relative;
            margin: 50px;
            padding: 20px;
            background: #f9f9f9;
        }
        
        .button {
            position: absolute;
            bottom: 10px;
            right: 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .tooltip {
            position: fixed;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 700;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .tooltip.visible {
            opacity: 1;
        }
        
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid rgba(0, 0, 0, 0.9);
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
        }
    </style>
</head>
<body>
    <h1>Tooltip Portal Test</h1>
    <p>This demonstrates how portals solve the overflow:hidden clipping issue.</p>
    
    <div class="container">
        <h3>Container with overflow: hidden</h3>
        <p>The button is positioned at the bottom-right corner. Without portals, the tooltip would be clipped.</p>
        <button class="button" id="testButton">Hover me!</button>
    </div>
    
    <!-- Portal container for tooltips -->
    <div id="tooltip-container"></div>
    
    <script>
        const button = document.getElementById('testButton');
        const tooltipContainer = document.getElementById('tooltip-container');
        let tooltip = null;
        
        function showTooltip() {
            if (tooltip) return;
            
            const rect = button.getBoundingClientRect();
            
            tooltip = document.createElement('div');
            tooltip.className = 'tooltip visible';
            tooltip.innerHTML = `
                This tooltip is rendered in a portal!
                <div class="arrow"></div>
            `;
            
            // Position tooltip above the button
            tooltip.style.left = (rect.left + rect.width / 2) + 'px';
            tooltip.style.top = (rect.top - 8) + 'px';
            tooltip.style.transform = 'translate(-50%, -100%)';
            
            tooltipContainer.appendChild(tooltip);
        }
        
        function hideTooltip() {
            if (tooltip) {
                tooltip.classList.remove('visible');
                setTimeout(() => {
                    if (tooltip && tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                    tooltip = null;
                }, 300);
            }
        }
        
        button.addEventListener('mouseenter', showTooltip);
        button.addEventListener('mouseleave', hideTooltip);
        button.addEventListener('focus', showTooltip);
        button.addEventListener('blur', hideTooltip);
    </script>
</body>
</html>
