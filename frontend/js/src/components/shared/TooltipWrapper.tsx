/** @jsx h */
import {h} from 'preact';
import {useCallback, useEffect, useLayoutEffect, useRef, useState} from 'preact/hooks';
import {createPortal} from 'preact/compat';
import {isMobileDevice} from '../../functions/device';
import type {ComponentChildren, RNode} from '../../types';

export type Props = Readonly<{
  tooltipMessage: string;
  children: ComponentChildren;
  position?: 'auto' | 'top' | 'bottom' | 'left' | 'right'; // Tooltip position options
  disabled?: boolean; // Whether the wrapped element is disabled
}>;

/**
 * TooltipWrapper component that displays tooltips on hover and focus.
 * Automatically positions tooltips based on screen location and available space.
 * Hides tooltips on mobile devices and for disabled elements.
 *
 * @param tooltipMessage - The text to display in the tooltip
 * @param children - The element(s) to wrap with tooltip functionality
 * @param position - Positioning preference ('auto' for smart positioning)
 * @param disabled - Whether the wrapped element is disabled (hides tooltip)
 */
const TooltipWrapper = ({tooltipMessage, children, position = 'auto', disabled = false}: Props): RNode => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [renderTooltip, setRenderTooltip] = useState(false);
  const [calculatedPosition, setCalculatedPosition] = useState<'top' | 'bottom' | 'left' | 'right'>('top');
  const [tooltipStyle, setTooltipStyle] = useState<{[key: string]: string | number}>({});
  const [arrowStyle, setArrowStyle] = useState<{[key: string]: string | number | undefined}>({});
  const wrapperRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const isMobile = isMobileDevice();

  /**
   * Calculates the visual center of the target element excluding CSS margins.
   * This ensures tooltips are positioned relative to the actual visible element content.
   */
  const getVisualElementCenter = useCallback(() => {
    if (!wrapperRef.current) {
      return {centerX: 0, centerY: 0, rect: null};
    }

    const rect = wrapperRef.current.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(wrapperRef.current);

    // Parse margin values (they can be in px, em, rem, etc.)
    const marginLeft = parseFloat(computedStyle.marginLeft) || 0;
    const marginRight = parseFloat(computedStyle.marginRight) || 0;
    const marginTop = parseFloat(computedStyle.marginTop) || 0;
    const marginBottom = parseFloat(computedStyle.marginBottom) || 0;

    // Calculate the visual bounds excluding margins
    const visualLeft = rect.left + marginLeft;
    const visualRight = rect.right - marginRight;
    const visualTop = rect.top + marginTop;
    const visualBottom = rect.bottom - marginBottom;

    const centerX = visualLeft + (visualRight - visualLeft) / 2;
    const centerY = visualTop + (visualBottom - visualTop) / 2;

    return {
      centerX,
      centerY,
      rect: {
        left: visualLeft,
        right: visualRight,
        top: visualTop,
        bottom: visualBottom,
        width: visualRight - visualLeft,
        height: visualBottom - visualTop,
      },
    };
  }, []);

  // Ref to store the tooltip show timeout
  const showTooltipTimeout = useRef<number | null>(null);

  /**
   * Calculates the optimal tooltip position based on available screen space
   * and the position of the target element.
   */
  const calculatePosition = useCallback((): 'top' | 'bottom' | 'left' | 'right' => {
    if (!wrapperRef.current || position !== 'auto') {
      return position === 'auto' ? 'top' : position;
    }

    const rect = wrapperRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Approximate tooltip dimensions
    const tooltipWidth = 200; // Will be adjusted based on content
    const tooltipHeight = 60;
    const margin = 16;

    // Calculate available space in each direction
    const spaceTop = rect.top;
    const spaceBottom = viewportHeight - rect.bottom;
    const spaceLeft = rect.left;

    // Determine position based on screen location and available space
    // Top-left quadrant: prefer bottom
    if (rect.left < viewportWidth / 2 && rect.top < viewportHeight / 2) {
      return spaceBottom >= tooltipHeight + margin ? 'bottom' : 'top';
    }

    // Top-right quadrant: prefer bottom
    if (rect.left >= viewportWidth / 2 && rect.top < viewportHeight / 2) {
      return spaceBottom >= tooltipHeight + margin ? 'bottom' : 'top';
    }

    // Bottom-left quadrant: prefer top
    if (rect.left < viewportWidth / 2 && rect.top >= viewportHeight / 2) {
      return spaceTop >= tooltipHeight + margin ? 'top' : 'bottom';
    }

    // Bottom-right quadrant: prefer left
    if (rect.left >= viewportWidth / 2 && rect.top >= viewportHeight / 2) {
      return spaceLeft >= tooltipWidth + margin ? 'left' : 'right';
    }

    return 'top'; // fallback
  }, [position]);

  /**
   * Calculates tooltip positioning styles based on the calculated position
   * and handles edge cases where tooltip needs to stretch to canvas edge.
   * Uses fixed positioning for portal rendering.
   */
  const calculateTooltipStyle = useCallback((): {[key: string]: string | number} => {
    if (!wrapperRef.current) {
      return {};
    }

    const viewportWidth = window.innerWidth;
    const margin = 8; // Unified margin for both tooltip and arrow

    const baseStyle: {[key: string]: string | number} = {
      position: 'fixed',
      backgroundColor: 'rgba(var(--color-text-rgb), 1)',
      color: 'white',
      padding: 'var(--panel-inner-half-spacing)',
      borderRadius: '0.375rem',
      fontSize: '14px',
      fontWeight: '700',
      zIndex: 1000,
      opacity: showTooltip ? 1 : 0,
      transition: 'opacity 0.3s ease',
      pointerEvents: 'none',
      boxShadow: 'none',
      border: 'none',
      whiteSpace: 'nowrap',
    };

    const pos = calculatedPosition;

    if (pos === 'top' || pos === 'bottom') {
      const estimatedWidth = Math.min(Math.max(tooltipMessage.length * 8 + 32, 120), 300);
      const halfTooltipWidth = estimatedWidth / 2;
      // Use visual center excluding margins
      const visualCenter = getVisualElementCenter();
      const elementCenterX = visualCenter.centerX;

      // Unified margin for both left and right edge detection
      const wouldOverflowLeft = elementCenterX - halfTooltipWidth < margin;
      const wouldOverflowRight = elementCenterX + halfTooltipWidth > viewportWidth - margin;
      let tooltipLeft: string | number;
      if (wouldOverflowLeft) {
        tooltipLeft = margin;
      } else if (wouldOverflowRight) {
        // Move tooltip as close as possible to the target, but not past the right edge
        const maxLeft = viewportWidth - estimatedWidth - margin;
        tooltipLeft = Math.max(elementCenterX - halfTooltipWidth, maxLeft);
      } else {
        tooltipLeft = elementCenterX;
      }

      const result: {[key: string]: string | number} = {
        ...baseStyle,
        left: tooltipLeft,
        transform: typeof tooltipLeft === 'number' && !wouldOverflowLeft && !wouldOverflowRight ? 'translateX(-50%)' : 'none',
        maxWidth: viewportWidth - 2 * margin,
      };

      if (pos === 'top') {
        result.top = visualCenter.rect!.top - 8;
        result.transform = result.transform === 'none' ? 'translateY(-100%)' : 'translate(-50%, -100%)';
      } else {
        result.top = visualCenter.rect!.bottom + 8;
      }

      return result;
    }

    // For left and right positions, use fixed positioning with visual center
    const visualCenter = getVisualElementCenter();
    switch (pos) {
      case 'left':
        return {
          ...baseStyle,
          left: visualCenter.rect!.left - 8,
          top: visualCenter.centerY,
          transform: 'translate(-100%, -50%)',
          whiteSpace: 'nowrap',
        };
      case 'right':
        return {
          ...baseStyle,
          left: visualCenter.rect!.right + 8,
          top: visualCenter.centerY,
          transform: 'translateY(-50%)',
          whiteSpace: 'nowrap',
        };
      default:
        return baseStyle;
    }
  }, [calculatedPosition, showTooltip, tooltipMessage, getVisualElementCenter]);

  /**
   * Calculates arrow positioning styles based on tooltip position.
   * Arrow always points to the center of the target element, even when tooltip is stretched.
   */
  const getArrowStyle = useCallback(() => {
    if (!wrapperRef.current) {
      return {};
    }

    const arrowSize = 6;
    const baseArrowStyle: {[key: string]: string | number} = {
      position: 'absolute',
      width: 0,
      height: 0,
    };

    const arrowColor = 'rgba(var(--color-text-rgb), 1)';

    switch (calculatedPosition) {
      case 'top':
      case 'bottom': {
        const tooltipElement = tooltipRef.current;
        if (!tooltipElement) {
          // Fallback to simple centered arrow if tooltip ref not available yet
          const verticalStyle = calculatedPosition === 'top' ? {
            top: '100%',
            borderLeft: `${arrowSize}px solid transparent`,
            borderRight: `${arrowSize}px solid transparent`,
            borderTop: `${arrowSize}px solid ${arrowColor}`,
          } : {
            bottom: '100%',
            borderLeft: `${arrowSize}px solid transparent`,
            borderRight: `${arrowSize}px solid transparent`,
            borderBottom: `${arrowSize}px solid ${arrowColor}`,
          };

          return {
            ...baseArrowStyle,
            ...verticalStyle,
            left: '50%',
            transform: 'translateX(-50%)',
          };
        }

        const tooltipRect = tooltipElement.getBoundingClientRect();
        // Use visual center excluding margins
        const visualCenter = getVisualElementCenter();
        const elementCenterX = visualCenter.centerX;
        const tooltipLeftX = tooltipRect.left;
        const tooltipWidth = tooltipRect.width;

        // Check if tooltip is normally centered (not repositioned due to edge detection)
        const tooltipCenterX = tooltipLeftX + tooltipWidth / 2;
        const isNormallyCentered = Math.abs(elementCenterX - tooltipCenterX) < 10;

        const verticalStyle = calculatedPosition === 'top' ? {
          top: '100%',
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderTop: `${arrowSize}px solid ${arrowColor}`,
        } : {
          bottom: '100%',
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid ${arrowColor}`,
        };

        if (isNormallyCentered) {
          // Tooltip is centered - use simple centered arrow
          return {
            ...baseArrowStyle,
            ...verticalStyle,
            left: '50%',
            transform: 'translateX(-50%)',
          };
        }
        // Tooltip is repositioned due to edge detection - calculate precise arrow position
        // Use actual tooltip position from DOM instead of estimated position
        const actualTooltipLeft = tooltipLeftX;
        const arrowLeftOffsetCorrected = elementCenterX - actualTooltipLeft;

        // Clamp arrow within tooltip bounds with minimal padding
        const minOffset = arrowSize; // allow arrow to reach the very edge
        const maxOffset = tooltipWidth - arrowSize;
        const clampedOffset = Math.max(minOffset, Math.min(maxOffset, arrowLeftOffsetCorrected));

        return {
          ...baseArrowStyle,
          ...verticalStyle,
          left: `${clampedOffset}px`,
        };
      }
      case 'left':
        return {
          ...baseArrowStyle,
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderLeft: `${arrowSize}px solid ${arrowColor}`,
          left: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
        };
      case 'right':
        return {
          ...baseArrowStyle,
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid ${arrowColor}`,
          right: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
        };
      default:
        return baseArrowStyle;
    }
  }, [calculatedPosition, getVisualElementCenter]);

  // Update calculated position when wrapper mounts or position prop changes
  useLayoutEffect(() => {
    const newPosition = calculatePosition();
    setCalculatedPosition(newPosition);
  }, [calculatePosition]);

  // Update tooltip styles when position or visibility changes
  useLayoutEffect(() => {
    let newStyle = calculateTooltipStyle();
    // Use actual width for right/left overflow only when tooltip is rendered
    if ((calculatedPosition === 'top' || calculatedPosition === 'bottom') && renderTooltip && tooltipRef.current && wrapperRef.current) {
      const tooltipRect = tooltipRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const margin = 8;
      // Use visual center excluding margins
      const visualCenter = getVisualElementCenter();
      const elementCenterX = visualCenter.centerX;
      const halfTooltipWidth = tooltipRect.width / 2;
      const wouldOverflowRight = elementCenterX + halfTooltipWidth > viewportWidth - margin;
      const wouldOverflowLeft = elementCenterX - halfTooltipWidth < margin;
      let tooltipLeft: string | number = newStyle.left;
      if (wouldOverflowLeft) {
        tooltipLeft = margin;
      } else if (wouldOverflowRight) {
        tooltipLeft = viewportWidth - tooltipRect.width - margin;
      } else {
        tooltipLeft = elementCenterX;
      }
      newStyle = {
        ...newStyle,
        left: tooltipLeft,
        transform: !wouldOverflowLeft && !wouldOverflowRight ? 'translateX(-50%)' : 'none',
      };
      if (calculatedPosition === 'top') {
        newStyle.transform = newStyle.transform === 'none' ? 'translateY(-100%)' : 'translate(-50%, -100%)';
      }
    }
    setTooltipStyle(newStyle);
  }, [calculateTooltipStyle, calculatedPosition, showTooltip, renderTooltip, tooltipMessage, getVisualElementCenter]);

  // Update arrow styles only when tooltip is visible to prevent flickering
  useLayoutEffect(() => {
    if (showTooltip && renderTooltip) {
      const newArrowStyle = getArrowStyle();
      setArrowStyle(newArrowStyle);
    }
  }, [showTooltip, renderTooltip, getArrowStyle, tooltipStyle.left]);

  // Two-phase rendering: render tooltip immediately when showing, delay removal when hiding
  useEffect(() => {
    if (showTooltip) {
      // Show immediately
      setRenderTooltip(true);
    } else {
      // Hide after transition completes
      const timer = setTimeout(() => {
        setRenderTooltip(false);
      }, 300); // Match the CSS transition duration
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [showTooltip]);

  // Consolidated event handlers to reduce code duplication
  const handleShowTooltip = useCallback(() => {
    if (!isMobile && !disabled && tooltipMessage) {
      if (showTooltipTimeout.current) {
        clearTimeout(showTooltipTimeout.current);
      }
      showTooltipTimeout.current = window.setTimeout(() => {
        setShowTooltip(true);
      }, 200); // 200ms delay before showing tooltip
    }
  }, [isMobile, disabled, tooltipMessage]);

  const handleHideTooltip = useCallback(() => {
    if (showTooltipTimeout.current) {
      clearTimeout(showTooltipTimeout.current);
      showTooltipTimeout.current = null;
    }
    setShowTooltip(false);
  }, []);

  // Check if child element is disabled
  const isChildDisabled = useCallback(() => {
    if (!wrapperRef.current) {
      return false;
    }

    const childElement = wrapperRef.current.firstElementChild as HTMLElement;
    if (!childElement) {
      return false;
    }

    // Check various ways an element can be disabled
    return (
      childElement.hasAttribute('disabled')
      || childElement.getAttribute('aria-disabled') === 'true'
      || childElement.classList.contains('disabled')
      || disabled
    );
  }, [disabled]);



  const wrapperStyle: {[key: string]: string | number} = {
    position: 'relative',
    display: 'inline-block',
    borderRadius: 'inherit',
  };

  // Get the tooltip container for portal rendering
  const tooltipContainer = typeof document !== 'undefined' ? document.getElementById('tooltip-container') : null;

  return (
    <div
      ref={wrapperRef}
      style={wrapperStyle}
      onMouseEnter={handleShowTooltip}
      onMouseLeave={handleHideTooltip}
      onFocus={handleShowTooltip}
      onBlur={handleHideTooltip}
    >
      {children}
      {tooltipMessage && !isMobile && !isChildDisabled() && tooltipContainer && renderTooltip && createPortal(
        <div
          ref={tooltipRef}
          style={tooltipStyle}
          role="tooltip"
          aria-hidden={!showTooltip}
        >
          {tooltipMessage}
          <div style={arrowStyle}></div>
        </div>,
        tooltipContainer,
      )}
    </div>
  );
};

export default TooltipWrapper;
