/** @jsx h */
import {h} from 'preact';
import {createPortal} from 'preact/compat';
import {useCallback, useEffect, useLayoutEffect, useRef, useState} from 'preact/hooks';

import {isMobileDevice} from '../../functions/device';
import {
  type TooltipPosition,
  calculateArrowPosition,
  calculateHorizontalPosition,
  calculateOptimalPosition,
  getVisualElementCenter,
  isChildElementDisabled,
} from '../../utils/tooltip';

import type {ComponentChildren, RNode} from '../../types';

export type Props = Readonly<{
  tooltipMessage: string;
  children: ComponentChildren;
  position?: 'auto' | TooltipPosition; // Tooltip position options
  disabled?: boolean; // Whether the wrapped element is disabled
}>;

/**
 * TooltipWrapper component that displays tooltips on hover and focus.
 * Automatically positions tooltips based on screen location and available space.
 * Hides tooltips on mobile devices and for disabled elements.
 *
 * @param tooltipMessage - The text to display in the tooltip
 * @param children - The element(s) to wrap with tooltip functionality
 * @param position - Positioning preference ('auto' for smart positioning)
 * @param disabled - Whether the wrapped element is disabled (hides tooltip)
 */
const TooltipWrapper = ({tooltipMessage, children, position = 'auto', disabled = false}: Props): RNode => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [renderTooltip, setRenderTooltip] = useState(false);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [calculatedPosition, setCalculatedPosition] = useState<TooltipPosition>('top');
  const [tooltipStyle, setTooltipStyle] = useState<{[key: string]: string | number}>({});
  const [arrowStyle, setArrowStyle] = useState<{[key: string]: string | number | undefined}>({});
  const wrapperRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const isMobile = isMobileDevice();

  // Get visual element center using utility function
  const getElementCenter = useCallback(() => {
    return wrapperRef.current ? getVisualElementCenter(wrapperRef.current) : {centerX: 0, centerY: 0, rect: null};
  }, []);

  // Ref to store the tooltip show timeout
  const showTooltipTimeout = useRef<number | null>(null);

  // Calculate optimal position using utility function
  const calculatePosition = useCallback((): TooltipPosition => {
    return wrapperRef.current ? calculateOptimalPosition(wrapperRef.current, position) : 'top';
  }, [position]);

  /**
   * Calculates tooltip positioning styles (only positioning, base styles are in CSS)
   */
  const calculateTooltipStyle = useCallback((): {[key: string]: string | number} => {
    if (!wrapperRef.current) {
      return {};
    }

    const viewportWidth = window.innerWidth;
    const margin = 8;
    const visualCenter = getElementCenter();
    const pos = calculatedPosition;

    if (pos === 'top' || pos === 'bottom') {
      const horizontalPos = calculateHorizontalPosition(visualCenter, tooltipMessage, viewportWidth, margin);

      const result: {[key: string]: string | number} = {
        left: horizontalPos.left,
        transform: horizontalPos.transform,
        maxWidth: horizontalPos.maxWidth,
      };

      if (pos === 'top') {
        result.top = visualCenter.rect!.top - margin;
        result.transform = result.transform === 'none' ? 'translateY(-100%)' : 'translate(-50%, -100%)';
      } else {
        result.top = visualCenter.rect!.bottom + margin;
      }

      return result;
    }

    // For left and right positions
    switch (pos) {
      case 'left':
        return {
          left: visualCenter.rect!.left - margin,
          top: visualCenter.centerY,
          transform: 'translate(-100%, -50%)',
        };
      case 'right':
        return {
          left: visualCenter.rect!.right + margin,
          top: visualCenter.centerY,
          transform: 'translateY(-50%)',
        };
      default:
        return {};
    }
  }, [calculatedPosition, tooltipMessage, getElementCenter]);

  /**
   * Calculates arrow positioning styles (base styles are in CSS)
   */
  const getArrowStyle = useCallback(() => {
    if (!wrapperRef.current || !tooltipRef.current) {
      return {};
    }

    if (calculatedPosition === 'top' || calculatedPosition === 'bottom') {
      const visualCenter = getElementCenter();
      const arrowPos = calculateArrowPosition(visualCenter, tooltipRef.current);
      return {
        left: arrowPos.left,
        transform: arrowPos.transform,
      };
    }

    // Left and right arrows are handled by CSS
    return {};
  }, [calculatedPosition, getElementCenter]);

  // Update calculated position when wrapper mounts or position prop changes
  useLayoutEffect(() => {
    const newPosition = calculatePosition();
    setCalculatedPosition(newPosition);
  }, [calculatePosition]);

  // Update tooltip styles when position or visibility changes
  useLayoutEffect(() => {
    let newStyle = calculateTooltipStyle();
    // Use actual width for right/left overflow only when tooltip is rendered
    if ((calculatedPosition === 'top' || calculatedPosition === 'bottom') && renderTooltip && tooltipRef.current && wrapperRef.current) {
      const tooltipRect = tooltipRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const margin = 8;
      // Use visual center excluding margins
      const visualCenter = getElementCenter();
      const elementCenterX = visualCenter.centerX;
      const halfTooltipWidth = tooltipRect.width / 2;
      const wouldOverflowRight = elementCenterX + halfTooltipWidth > viewportWidth - margin;
      const wouldOverflowLeft = elementCenterX - halfTooltipWidth < margin;
      let tooltipLeft: string | number = newStyle.left;
      if (wouldOverflowLeft) {
        tooltipLeft = margin;
      } else if (wouldOverflowRight) {
        tooltipLeft = viewportWidth - tooltipRect.width - margin;
      } else {
        tooltipLeft = elementCenterX;
      }
      newStyle = {
        ...newStyle,
        left: tooltipLeft,
        transform: !wouldOverflowLeft && !wouldOverflowRight ? 'translateX(-50%)' : 'none',
      };
      if (calculatedPosition === 'top') {
        newStyle.transform = newStyle.transform === 'none' ? 'translateY(-100%)' : 'translate(-50%, -100%)';
      }
    }
    setTooltipStyle(newStyle);
  }, [calculateTooltipStyle, calculatedPosition, renderTooltip, tooltipMessage, getElementCenter]);

  // Update arrow styles only when tooltip is visible to prevent flickering
  useLayoutEffect(() => {
    if (tooltipVisible && renderTooltip) {
      const newArrowStyle = getArrowStyle();
      setArrowStyle(newArrowStyle);
    }
  }, [tooltipVisible, renderTooltip, getArrowStyle, tooltipStyle.left]);

  // Three-phase rendering: render -> show with animation -> hide with animation -> remove
  useEffect(() => {
    if (showTooltip) {
      // Phase 1: Render tooltip in DOM with opacity 0
      setRenderTooltip(true);
      setTooltipVisible(false);

      // Phase 2: Small delay then animate to opacity 1
      const showTimer = setTimeout(() => {
        setTooltipVisible(true);
      }, 10); // Small delay to ensure DOM is ready for transition

      return () => clearTimeout(showTimer);
    }
    // Phase 3: Hide with animation (opacity 0)
    setTooltipVisible(false);

    // Phase 4: Remove from DOM after transition completes
    const hideTimer = setTimeout(() => {
      setRenderTooltip(false);
    }, 300); // Match the CSS transition duration

    return () => clearTimeout(hideTimer);

  }, [showTooltip]);

  const handleShowTooltip = useCallback(() => {
    if (!isMobile && !disabled && tooltipMessage) {
      if (showTooltipTimeout.current) {
        clearTimeout(showTooltipTimeout.current);
      }
      showTooltipTimeout.current = window.setTimeout(() => {
        setShowTooltip(true);
      }, 200); // 200ms delay before showing tooltip
    }
  }, [isMobile, disabled, tooltipMessage]);

  const handleHideTooltip = useCallback(() => {
    if (showTooltipTimeout.current) {
      clearTimeout(showTooltipTimeout.current);
      showTooltipTimeout.current = null;
    }
    setShowTooltip(false);
  }, []);

  // Check if child element is disabled using utility function
  const isChildDisabled = useCallback(() => {
    return wrapperRef.current ? isChildElementDisabled(wrapperRef.current, disabled) : false;
  }, [disabled]);

  // Get the tooltip container for portal rendering
  const tooltipContainer = typeof document !== 'undefined' ? document.getElementById('tooltip-container') : null;

  return (
    <div
      ref={wrapperRef}
      className="vm-tooltip-wrapper"
      onMouseEnter={handleShowTooltip}
      onMouseLeave={handleHideTooltip}
      onFocus={handleShowTooltip}
      onBlur={handleHideTooltip}
    >
      {children}
      {tooltipMessage && !isMobile && !isChildDisabled() && tooltipContainer && renderTooltip && createPortal(
        <div
          ref={tooltipRef}
          className={`vm-tooltip vm-tooltip--${calculatedPosition} ${!tooltipVisible ? 'vm-tooltip--hidden' : ''}`}
          style={tooltipStyle}
          role="tooltip"
          aria-hidden={!tooltipVisible}
        >
          {tooltipMessage}
          <div
            className={`vm-tooltip-arrow vm-tooltip-arrow--${calculatedPosition}`}
            style={arrowStyle}
          ></div>
        </div>,
        tooltipContainer,
      )}
    </div>
  );
};

export default TooltipWrapper;
