/** @jsx h */
import {h} from 'preact';
import {useCallback, useEffect, useLayoutEffect, useRef, useState} from 'preact/hooks';
import {createPortal} from 'preact/compat';
import {isMobileDevice} from '../../functions/device';
import type {ComponentChildren, RNode} from '../../types';

export type Props = Readonly<{
  tooltipMessage: string;
  children: ComponentChildren;
  position?: 'auto' | 'top' | 'bottom' | 'left' | 'right'; // Tooltip position options
  disabled?: boolean; // Whether the wrapped element is disabled
}>;

/**
 * TooltipWrapper component that displays tooltips on hover and focus.
 * Automatically positions tooltips based on screen location and available space.
 * Hides tooltips on mobile devices and for disabled elements.
 *
 * @param tooltipMessage - The text to display in the tooltip
 * @param children - The element(s) to wrap with tooltip functionality
 * @param position - Positioning preference ('auto' for smart positioning)
 * @param disabled - Whether the wrapped element is disabled (hides tooltip)
 */
const TooltipWrapper = ({tooltipMessage, children, position = 'auto', disabled = false}: Props): RNode => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [renderTooltip, setRenderTooltip] = useState(false);
  const [calculatedPosition, setCalculatedPosition] = useState<'top' | 'bottom' | 'left' | 'right'>('top');
  const [tooltipStyle, setTooltipStyle] = useState<{[key: string]: string | number}>({});
  const [arrowStyle, setArrowStyle] = useState<{[key: string]: string | number | undefined}>({});
  const wrapperRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const isMobile = isMobileDevice();

  /**
   * Calculates the optimal tooltip position based on available screen space
   * and the position of the target element.
   */
  const calculatePosition = useCallback((): 'top' | 'bottom' | 'left' | 'right' => {
    if (!wrapperRef.current || position !== 'auto') {
      return position === 'auto' ? 'top' : position;
    }

    const rect = wrapperRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Approximate tooltip dimensions
    const tooltipWidth = 200; // Will be adjusted based on content
    const tooltipHeight = 60;
    const margin = 16;

    // Calculate available space in each direction
    const spaceTop = rect.top;
    const spaceBottom = viewportHeight - rect.bottom;
    const spaceLeft = rect.left;

    // Determine position based on screen location and available space
    // Top-left quadrant: prefer bottom
    if (rect.left < viewportWidth / 2 && rect.top < viewportHeight / 2) {
      return spaceBottom >= tooltipHeight + margin ? 'bottom' : 'top';
    }

    // Top-right quadrant: prefer bottom
    if (rect.left >= viewportWidth / 2 && rect.top < viewportHeight / 2) {
      return spaceBottom >= tooltipHeight + margin ? 'bottom' : 'top';
    }

    // Bottom-left quadrant: prefer top
    if (rect.left < viewportWidth / 2 && rect.top >= viewportHeight / 2) {
      return spaceTop >= tooltipHeight + margin ? 'top' : 'bottom';
    }

    // Bottom-right quadrant: prefer left
    if (rect.left >= viewportWidth / 2 && rect.top >= viewportHeight / 2) {
      return spaceLeft >= tooltipWidth + margin ? 'left' : 'right';
    }

    return 'top'; // fallback
  }, [position]);

  /**
   * Calculates tooltip positioning styles based on the calculated position
   * and handles edge cases where tooltip needs to stretch to canvas edge.
   * Uses fixed positioning for portal rendering.
   */
  const calculateTooltipStyle = useCallback((): {[key: string]: string | number} => {
    if (!wrapperRef.current) {
      return {};
    }

    const wrapperRect = wrapperRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const margin = 8; // Minimum margin from viewport edge

    const baseStyle: {[key: string]: string | number} = {
      position: 'fixed',
      backgroundColor: 'rgba(var(--color-text-rgb), 1)',
      color: 'white',
      padding: 'var(--panel-inner-half-spacing)',
      borderRadius: '0.375rem',
      fontSize: '14px',
      fontWeight: '700',
      zIndex: 1000,
      opacity: showTooltip ? 1 : 0,
      transition: 'opacity 0.3s ease',
      pointerEvents: 'none',
      boxShadow: 'none',
      border: 'none',
      whiteSpace: 'nowrap',
    };

    const pos = calculatedPosition;

    // For top and bottom positions, implement edge detection and stretching
    if (pos === 'top' || pos === 'bottom') {
      // Estimate tooltip width based on content length (rough approximation)
      const estimatedWidth = Math.min(Math.max(tooltipMessage.length * 8 + 32, 120), 300);
      const halfTooltipWidth = estimatedWidth / 2;

      // Calculate ideal center position
      const elementCenterX = wrapperRect.left + wrapperRect.width / 2;

      // Check if tooltip would overflow viewport
      const wouldOverflowLeft = elementCenterX - halfTooltipWidth < margin;
      const wouldOverflowRight = elementCenterX + halfTooltipWidth > viewportWidth - margin;

      let tooltipLeft: string | number;

      if (wouldOverflowLeft || wouldOverflowRight) {
        // Tooltip needs to be repositioned to fit viewport
        if (wouldOverflowLeft) {
          // Anchor to left edge with margin
          tooltipLeft = margin;
        } else {
          // Anchor to right edge - position so tooltip right edge is at viewport right edge minus margin
          tooltipLeft = viewportWidth - estimatedWidth - margin;
        }
      } else {
        // Normal centered positioning relative to element
        tooltipLeft = elementCenterX;
      }

      const result: {[key: string]: string | number} = {
        ...baseStyle,
        left: tooltipLeft,
        transform: typeof tooltipLeft === 'number' && !wouldOverflowLeft && !wouldOverflowRight ? 'translateX(-50%)' : 'none',
        maxWidth: viewportWidth - 2 * margin,
      };

      if (pos === 'top') {
        result.top = wrapperRect.top - 8; // 8px margin above element
        result.transform = result.transform === 'none' ? 'translateY(-100%)' : 'translate(-50%, -100%)';
      } else {
        result.top = wrapperRect.bottom + 8; // 8px margin below element
      }

      return result;
    }

    // For left and right positions, use fixed positioning
    switch (pos) {
      case 'left':
        return {
          ...baseStyle,
          left: wrapperRect.left - 8,
          top: wrapperRect.top + wrapperRect.height / 2,
          transform: 'translate(-100%, -50%)',
          whiteSpace: 'nowrap',
        };
      case 'right':
        return {
          ...baseStyle,
          left: wrapperRect.right + 8,
          top: wrapperRect.top + wrapperRect.height / 2,
          transform: 'translateY(-50%)',
          whiteSpace: 'nowrap',
        };
      default:
        return baseStyle;
    }
  }, [calculatedPosition, showTooltip, tooltipMessage]);

  /**
   * Calculates arrow positioning styles based on tooltip position.
   * Arrow always points to the center of the target element, even when tooltip is stretched.
   */
  const getArrowStyle = useCallback(() => {
    if (!wrapperRef.current) {
      return {};
    }

    const arrowSize = 6;
    const baseArrowStyle: {[key: string]: string | number} = {
      position: 'absolute',
      width: 0,
      height: 0,
    };

    const arrowColor = 'rgba(var(--color-text-rgb), 1)';

    switch (calculatedPosition) {
      case 'top':
      case 'bottom': {
        // Check if tooltip is using normal centering or stretched positioning
        const tooltipElement = tooltipRef.current;
        if (!tooltipElement) {
          // Fallback to simple centered arrow if tooltip ref not available yet
          const verticalStyle = calculatedPosition === 'top' ? {
            top: '100%',
            borderLeft: `${arrowSize}px solid transparent`,
            borderRight: `${arrowSize}px solid transparent`,
            borderTop: `${arrowSize}px solid ${arrowColor}`,
          } : {
            bottom: '100%',
            borderLeft: `${arrowSize}px solid transparent`,
            borderRight: `${arrowSize}px solid transparent`,
            borderBottom: `${arrowSize}px solid ${arrowColor}`,
          };

          return {
            ...baseArrowStyle,
            ...verticalStyle,
            left: '50%',
            transform: 'translateX(-50%)',
          };
        }

        const wrapperRect = wrapperRef.current.getBoundingClientRect();
        const tooltipRect = tooltipElement.getBoundingClientRect();

        // Calculate where the arrow should point (center of target element)
        const elementCenterX = wrapperRect.left + wrapperRect.width / 2;
        const tooltipLeftX = tooltipRect.left;

        // Check if tooltip is normally centered (not repositioned due to edge detection)
        // Use the same margin value as tooltip positioning for consistency
        const margin = 8;
        const viewportWidth = window.innerWidth;
        const tooltipCenterX = tooltipLeftX + tooltipRect.width / 2;
        const isNormallyCentered = Math.abs(elementCenterX - tooltipCenterX) < 10;

        const verticalStyle = calculatedPosition === 'top' ? {
          top: '100%',
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderTop: `${arrowSize}px solid ${arrowColor}`,
        } : {
          bottom: '100%',
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid ${arrowColor}`,
        };

        if (isNormallyCentered) {
          // Tooltip is centered - use simple centered arrow
          return {
            ...baseArrowStyle,
            ...verticalStyle,
            left: '50%',
            transform: 'translateX(-50%)',
          };
        }

        // Tooltip is repositioned due to edge detection - calculate precise arrow position
        // Detect which edge case we're in to ensure consistent positioning with tooltip logic
        const estimatedWidth = Math.min(Math.max(tooltipMessage.length * 8 + 32, 120), 300);
        const halfTooltipWidth = estimatedWidth / 2;
        const wouldOverflowLeft = elementCenterX - halfTooltipWidth < margin;
        const wouldOverflowRight = elementCenterX + halfTooltipWidth > viewportWidth - margin;

        let expectedTooltipLeft: number;
        if (wouldOverflowLeft) {
          expectedTooltipLeft = margin;
        } else if (wouldOverflowRight) {
          expectedTooltipLeft = viewportWidth - estimatedWidth - margin;
        } else {
          expectedTooltipLeft = elementCenterX - halfTooltipWidth;
        }

        // Calculate arrow position based on expected tooltip position
        const arrowLeftOffsetCorrected = elementCenterX - expectedTooltipLeft;
        const minOffset = arrowSize + 4;
        const maxOffset = tooltipRect.width - arrowSize - 4;
        const clampedOffset = Math.max(minOffset, Math.min(maxOffset, arrowLeftOffsetCorrected));

        return {
          ...baseArrowStyle,
          ...verticalStyle,
          left: `${clampedOffset}px`,
        };

      }
      case 'left':
        return {
          ...baseArrowStyle,
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderLeft: `${arrowSize}px solid ${arrowColor}`,
          left: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
        };
      case 'right':
        return {
          ...baseArrowStyle,
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid ${arrowColor}`,
          right: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
        };
      default:
        return baseArrowStyle;
    }
  }, [calculatedPosition, tooltipMessage]);

  // Update calculated position when wrapper mounts or position prop changes
  useLayoutEffect(() => {
    const newPosition = calculatePosition();
    setCalculatedPosition(newPosition);
  }, [calculatePosition]);

  // Update tooltip styles when position or visibility changes
  useLayoutEffect(() => {
    const newStyle = calculateTooltipStyle();
    setTooltipStyle(newStyle);
  }, [calculateTooltipStyle]);

  // Update arrow styles only when tooltip is visible to prevent flickering
  useLayoutEffect(() => {
    if (showTooltip && renderTooltip) {
      const newArrowStyle = getArrowStyle();
      setArrowStyle(newArrowStyle);
    }
  }, [showTooltip, renderTooltip, getArrowStyle]);

  // Two-phase rendering: render tooltip immediately when showing, delay removal when hiding
  useEffect(() => {
    if (showTooltip) {
      // Show immediately
      setRenderTooltip(true);
    } else {
      // Hide after transition completes
      const timer = setTimeout(() => {
        setRenderTooltip(false);
      }, 300); // Match the CSS transition duration
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [showTooltip]);

  // Consolidated event handlers to reduce code duplication
  const handleShowTooltip = useCallback(() => {
    if (!isMobile && !disabled && tooltipMessage) {
      setShowTooltip(true);
    }
  }, [isMobile, disabled, tooltipMessage]);

  const handleHideTooltip = useCallback(() => {
    setShowTooltip(false);
  }, []);

  // Check if child element is disabled
  const isChildDisabled = useCallback(() => {
    if (!wrapperRef.current) {
      return false;
    }

    const childElement = wrapperRef.current.firstElementChild as HTMLElement;
    if (!childElement) {
      return false;
    }

    // Check various ways an element can be disabled
    return (
      childElement.hasAttribute('disabled')
      || childElement.getAttribute('aria-disabled') === 'true'
      || childElement.classList.contains('disabled')
      || disabled
    );
  }, [disabled]);



  const wrapperStyle: {[key: string]: string | number} = {
    position: 'relative',
    display: 'inline-block',
    borderRadius: 'inherit',
  };

  // Get the tooltip container for portal rendering
  const tooltipContainer = typeof document !== 'undefined' ? document.getElementById('tooltip-container') : null;

  return (
    <div
      ref={wrapperRef}
      style={wrapperStyle}
      onMouseEnter={handleShowTooltip}
      onMouseLeave={handleHideTooltip}
      onFocus={handleShowTooltip}
      onBlur={handleHideTooltip}
    >
      {children}
      {tooltipMessage && !isMobile && !isChildDisabled() && tooltipContainer && renderTooltip && createPortal(
        <div
          ref={tooltipRef}
          style={tooltipStyle}
          role="tooltip"
          aria-hidden={!showTooltip}
        >
          {tooltipMessage}
          <div style={arrowStyle}></div>
        </div>,
        tooltipContainer,
      )}
    </div>
  );
};

export default TooltipWrapper;
