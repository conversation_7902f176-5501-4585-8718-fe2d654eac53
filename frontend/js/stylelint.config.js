// Config for CSS lint checking including as embedded in javascript

module.exports = {
  extends: [
    'stylelint-config-recommended',
  ],
  ignoreFiles: [
    '**/*.ts',
    '**/*.html',
  ],
  overrides: [{
    customSyntax: '@stylelint/postcss-css-in-js',
    files: ['**/*.js', '**/*.tsx'],
    rules: {
      'declaration-property-value-no-unknown': null,
    },
  }],
  rules: {
    'no-descending-specificity': null,
  },
};
