import babelParser from '@babel/eslint-parser';
import {fixupConfigRules} from '@eslint/compat';
import {FlatCompat} from '@eslint/eslintrc';
import js from '@eslint/js';
import stylistic from '@stylistic/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import {defineConfig, globalIgnores} from 'eslint/config';
import globals from 'globals';
import path from 'node:path';
import {fileURLToPath} from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  allConfig: js.configs.all,
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
});

const sensibleRules = {
  '@stylistic/array-bracket-newline': ['warn', 'consistent'],
  '@stylistic/array-bracket-spacing': 'warn',
  '@stylistic/arrow-spacing': 'warn',
  '@stylistic/brace-style': 'warn',
  '@stylistic/comma-dangle': ['warn', 'always-multiline'],
  '@stylistic/comma-spacing': 'warn',
  '@stylistic/comma-style': 'warn',
  '@stylistic/computed-property-spacing': 'warn',
  '@stylistic/eol-last': 'warn',
  '@stylistic/func-call-spacing': 'warn',
  '@stylistic/indent': ['warn', 2, {
    SwitchCase: 1,
    offsetTernaryExpressions: true,
  }],
  '@stylistic/key-spacing': 'warn',
  '@stylistic/keyword-spacing': 'warn',
  '@stylistic/no-multi-spaces': 'error',
  '@stylistic/no-trailing-spaces': 'warn',
  '@stylistic/no-whitespace-before-property': 'warn',
  '@stylistic/object-curly-newline': 'warn',
  '@stylistic/object-curly-spacing': 'warn',
  '@stylistic/operator-linebreak': ['warn', 'before'],
  '@stylistic/quotes': ['warn', 'single'],
  '@stylistic/semi': 'warn',
  '@stylistic/semi-style': 'warn',
  '@stylistic/space-before-blocks': ['warn', 'always'],
  '@stylistic/space-before-function-paren': ['warn', 'never'],
  '@stylistic/space-in-parens': 'warn',

  // TODO: Switching block-spacing to stylistic affects type declarations
  'block-spacing': 'warn',
  'complexity': ['warn', 20],
  'consistent-return': 'error',
  'curly': 'error',
  'eqeqeq': ['error', 'smart'],
  'implicit-arrow-linebreak': 'warn',
  'no-else-return': 'error',
  'no-var': 'error',
  'object-shorthand': 'warn',
  'one-var': ['error', 'never'],
  'prefer-const': 'warn',
  'quote-props': ['warn', 'consistent'],
  'sort-imports': ['warn', {
    allowSeparatedGroups: true,
    ignoreDeclarationSort: true,
  }],
  // TODO: Switching space-infix-ops stylistic affects type declarations
  'space-infix-ops': 'warn',
  'unicode-bom': 'error',
};

const settingsReact = {
  react: {
    version: '16.13',
  },
};

export default defineConfig([
  // General rules
  js.configs.recommended,
  globalIgnores([
    'dist/**/*',
  ]),
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        process: false,
      },

      parser: babelParser,
    },
    plugins: {
      '@stylistic': stylistic,
    },
  },

  // Rules for .js files
  ...fixupConfigRules(
    compat.extends('plugin:react/recommended', 'plugin:react-hooks/recommended'),
  ).map(config => ({
    ...config,
    files: ['**/src/*.js', '**/src/**/*.js'],
  })),
  {
    files: ['**/src/*.js', '**/src/**/*.js'],
    rules: {
      'no-unused-vars': ['error', {
        varsIgnorePattern: '^enforce|^is|^_$',
      }],
      'no-use-before-define': ['error', {
        functions: false,
        variables: false,
      }],
      'react/prop-types': 'off',
    },
    settings: settingsReact,
  },

  // Rules for .ts files
  ...fixupConfigRules(compat.extends(
    'plugin:@typescript-eslint/recommended',
  )).map(config => ({
    ...config,
    files: ['**/src/*.ts', '**/src/**/*.ts'],
    languageOptions: {
      parser: tsParser,
    },
  })),
  {
    files: ['**/src/*.ts', '**/src/**/*.ts'],
    languageOptions: {
      parser: tsParser,
    },
    rules: {
      '@stylistic/member-delimiter-style': 'warn',
      '@typescript-eslint/consistent-indexed-object-style': ['warn', 'index-signature'],
      '@typescript-eslint/consistent-type-imports': 'warn',
      '@typescript-eslint/explicit-module-boundary-types': 'warn',
      '@typescript-eslint/no-unused-vars': ['warn', {
        argsIgnorePattern: '^_',
        caughtErrorsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
      }],
      ...sensibleRules,
    },
  },

  // Rules for .tsx files
  ...fixupConfigRules(compat.extends(
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
  )).map(config => ({
    ...config,
    files: ['**/src/*.tsx', '**/src/**/*.tsx'],
    languageOptions: {
      parser: tsParser,
    },
  })),
  {
    files: ['**/src/*.tsx', '**/src/**/*.tsx'],
    languageOptions: {
      parser: tsParser,
    },
    rules: {
      '@stylistic/member-delimiter-style': 'warn',

      '@typescript-eslint/consistent-indexed-object-style': ['warn', 'index-signature'],
      '@typescript-eslint/consistent-type-imports': 'warn',
      '@typescript-eslint/explicit-module-boundary-types': 'warn',
      '@typescript-eslint/no-unused-vars': ['warn', {
        argsIgnorePattern: '^_',
        caughtErrorsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
      }],

      'jsx-quotes': 'warn',

      'react/jsx-curly-newline': 'warn',
      'react/jsx-curly-spacing': 'warn',
      'react/jsx-first-prop-new-line': 'warn',
      'react/jsx-indent': ['warn', 2],
      'react/jsx-indent-props': ['warn', 2],
      // No browser has the dumb behaviour with target="_blank" any more.
      'react/jsx-no-target-blank': 'off',
      'react/jsx-props-no-multi-spaces': 'warn',
      'react/jsx-tag-spacing': 'warn',
      // Allow lowercased tabindex which is required for SVG
      'react/no-unknown-property': ['error', {
        ignore: ['tabindex'],
      }],
      ...sensibleRules,
    },
    settings: settingsReact,
  },

  // Rules for .spec.tsx files
  ...compat.extends('plugin:jest/recommended').map(config => ({
    ...config,
    files: ['**/*.spec.tsx'],
  })),
  {
    files: ['**/*.spec.tsx'],
    languageOptions: {
      globals: {
        ...globals.jest.globals,
      },
    },
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },

  // Rules for config files
  {
    files: ['**/*.babel.js', '**/.*.js', '**/*.config.{js,mjs}'],
    languageOptions: {
      globals: {
        '__dirname': false,
        'module': false,
      },
    },
    rules: {
      'sort-keys': 'warn',
      ...sensibleRules,
    },
  },
]);
